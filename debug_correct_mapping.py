"""
调试正确的股票代码映射
找出01760 (INTRON TECH) 的正确PDF代码
"""

import requests
from datetime import datetime
import time


def test_stock_code_variations():
    """测试不同的股票代码变体"""
    print("测试01760 (INTRON TECH) 的正确PDF代码...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # 测试日期 - 使用一个我们知道有文档的日期
    test_date = "20241129"  # 2024年11月29日
    year = "2024"
    month_day = "1129"
    
    # 可能的股票代码变体
    possible_codes = [
        "01760",  # 原始代码
        "1760",   # 去掉前导零
        "00741",  # 之前错误使用的代码
        "741",    # 741的变体
        "001760", # 6位代码
        "017600", # 6位代码变体
        "176000", # 其他变体
    ]
    
    base_url = "https://www1.hkexnews.hk/listedco/listconews/sehk"
    
    print(f"测试日期: {test_date}")
    print(f"基础URL: {base_url}/{year}/{month_day}/")
    
    existing_urls = []
    
    for code in possible_codes:
        print(f"\n测试股票代码: {code}")
        
        # 测试不同的URL格式
        test_urls = [
            f"{base_url}/{year}/{month_day}/{test_date}{code}.pdf",
            f"{base_url}/{year}/{month_day}/{test_date}{code}_e.pdf",
            f"{base_url}/{year}/{month_day}/{test_date}{code}_c.pdf",
            f"{base_url}/{year}/{month_day}/LTN{test_date}{code}.pdf",
            f"{base_url}/{year}/{month_day}/LTN{test_date}{code}_e.pdf",
            f"{base_url}/{year}/{month_day}/LTN{test_date}{code}_c.pdf",
        ]
        
        for url in test_urls:
            try:
                response = session.head(url, timeout=10)
                if response.status_code == 200:
                    print(f"  ✓ 存在: {url}")
                    existing_urls.append(url)
                else:
                    print(f"  ✗ {response.status_code}: {url}")
                    
                time.sleep(0.2)  # 小延迟
                
            except Exception as e:
                print(f"  ✗ 错误: {url}")
    
    print(f"\n找到的有效URL:")
    for url in existing_urls:
        print(f"  {url}")
    
    return existing_urls


def test_intron_tech_search():
    """尝试通过搜索找到INTRON TECH的正确信息"""
    print("\n" + "="*50)
    print("尝试通过不同方法找到INTRON TECH的信息...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # 1. 尝试访问HKEX的公司信息页面
    try:
        # 尝试访问公司列表或搜索页面
        search_urls = [
            "https://www1.hkexnews.hk/search/titlesearch.xhtml",
            "https://www.hkex.com.hk/Market-Data/Securities-Prices/Equities",
            "https://di.hkex.com.hk/di/NSSrchCorpQW.aspx?src=MAIN&lang=EN&in=1&sc=01760",
        ]
        
        for url in search_urls:
            print(f"\n尝试访问: {url}")
            try:
                response = session.get(url, timeout=30)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    # 保存页面用于分析
                    filename = f"page_{url.split('/')[-1].replace('?', '_').replace('=', '_')}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"页面已保存到: {filename}")
                    
                    # 查找INTRON相关信息
                    if 'INTRON' in response.text.upper():
                        print("✓ 页面包含INTRON信息")
                    else:
                        print("✗ 页面不包含INTRON信息")
                
            except Exception as e:
                print(f"访问失败: {e}")
    
    except Exception as e:
        print(f"搜索过程出错: {e}")


def analyze_url_patterns():
    """分析已知存在的URL，找出模式"""
    print("\n" + "="*50)
    print("分析URL模式...")
    
    # 我们之前发现的存在的URL
    known_urls = [
        "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1129/2024112900741.pdf",
        "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1106/2024110600741.pdf",
        "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1205/2024120500741_c.pdf",
    ]
    
    print("已知存在的URL:")
    for url in known_urls:
        print(f"  {url}")
        
        # 提取信息
        parts = url.split('/')
        filename = parts[-1]
        date_part = parts[-2]
        
        print(f"    文件名: {filename}")
        print(f"    日期目录: {date_part}")
        
        # 分析文件名
        if filename.endswith('_c.pdf'):
            print(f"    -> 中文版PDF")
            base_name = filename[:-6]  # 去掉_c.pdf
        elif filename.endswith('_e.pdf'):
            print(f"    -> 英文版PDF")
            base_name = filename[:-6]  # 去掉_e.pdf
        else:
            print(f"    -> 默认版PDF")
            base_name = filename[:-4]  # 去掉.pdf
        
        print(f"    基础名称: {base_name}")
        
        # 提取股票代码
        if len(base_name) >= 5:
            possible_stock_code = base_name[-5:]  # 最后5位
            print(f"    可能的股票代码: {possible_stock_code}")


def main():
    """主函数"""
    print("调试INTRON TECH (01760) 的正确股票代码映射")
    print("="*60)
    
    # 1. 测试不同的股票代码变体
    existing_urls = test_stock_code_variations()
    
    # 2. 尝试搜索INTRON TECH信息
    test_intron_tech_search()
    
    # 3. 分析URL模式
    analyze_url_patterns()
    
    print("\n" + "="*60)
    print("总结:")
    if existing_urls:
        print(f"找到 {len(existing_urls)} 个有效URL")
        print("建议检查这些URL对应的公司是否为INTRON TECH")
    else:
        print("没有找到有效的URL")
        print("可能需要:")
        print("1. 检查01760是否为正确的股票代码")
        print("2. 确认INTRON TECH是否在香港交易所上市")
        print("3. 检查公司是否有其他名称或代码")


if __name__ == "__main__":
    main()
