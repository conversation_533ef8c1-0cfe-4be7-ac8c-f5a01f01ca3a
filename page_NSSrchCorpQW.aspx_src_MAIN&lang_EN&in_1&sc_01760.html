

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<!-- For EPS search -->



<!-- For EPS search end -->



<html class="news-hkex">

	<HEAD>

		<title>Hong Kong Exchanges and Clearing Limited</title>

    		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">    

	        <meta name="viewport" content="width=device-width, initial-scale=1">

                <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    		<link href="/ncms/css/main.css" rel="stylesheet" />

    		<script type="text/javascript" src="/ncms/script/hkex_app.js"></script>

    		<script type="text/javascript" src="/ncms/script/hkex_settings.js"></script>

    		<script type="text/javascript" src="/ncms/script/hkex_widget.js"></script>

    		<script type="text/javascript" src="/ncms/script/vendor.js"></script>

		<LINK href="../css/hkex_css.css" type="text/css" rel="stylesheet">

	<script type="text/javascript">

	

	// JS variables, please set before hkex_pagebanner.js

	var pageDefaultTitle = "Search by listed corporation - Current Securities"; 

	// TO override page title 

        var pageDefaultBanner = "/ncms/media/HKEXnews/top_banner_bg.png"; // TO override page banner for desktop

        var pageDefaultTabletBanner = "/ncms/media/HKEXnews/top_banner_bg_tablet.png"; // TO override page banner for tablet

        // TO override breadcrumb

	

       	var overrideBreadcrumb = [{

            title: "Disclosure Of Interests (Notices Filed Through DION System Since 3 July 2017)",

            url: "https://www2.hkexnews.hk/Shareholding-Disclosures/Disclosure-of-Interests/Search-facilities---DI-from-3-July-2017?sc_lang=en"

        },

	{            

            title: "Disclosure Of Interests",

            url: "https://www2.hkexnews.hk/Shareholding-Disclosures/Disclosure-of-Interests?sc_lang=en"

        },

	{            

            title: "Shareholding Disclosures",

            url: "https://www2.hkexnews.hk/Shareholding-Disclosures?sc_lang=en"

        }];

		

        var overridePageTools = {};

        overridePageTools.showlastupdate = 0; // 1 for ture, 0 for false

        overridePageTools.lastupdate = "This is last updated date";

        overridePageTools.showprint = 0; // 1 for ture, 0 for false

        overridePageTools.printfriend = "Print FRIENDLY";        

	</script>

        <!-- Clickjacking Fix-->

    <style id="antiClickjack">

        body {

            display: none !important;

        }

    </style>   

    <script type="text/javascript" language="javascript" src="../js/hkex_clickjack.js"></script>



			<!-- PCCW Revamped 20040313 -->

			

		<!-- -->



    



	</HEAD>

<body>

    <a name="TOP"></a>

			

    <!-- CMS Control Header START -->

    <script type="text/javascript" src="/ncms/script/hkex_head.js"></script>

    <!-- CMS Control Header END -->



    <!-- CMS Control PageBanner START -->

    <script type="text/javascript" src="/ncms/script/hkex_pagebanner.js"></script>

    <!-- CMS Control PageBanner END -->

				



    <div id="skip-to-content"></div>



    <div class="content-container content-container-reset">

        <main class="di-page-content">

            <div class="container section-panel">

		<!-- PCCW Revamped 20040313 -->

		

		

		<table height="100%" cellSpacing="3" cellPadding="5" width="100%" bgColor="#ffffff" border="0">

			<tr>

				<td vAlign="top">

					

					<!-- -->

					<form method="post" action="NSSrchCorpQW.aspx?src=MAIN&amp;lang=EN&amp;in=1&amp;sc=01760&amp;" id="NSSrchCorp">

<div class="aspNetHidden">

<input type="hidden" name="__EVENTTARGET" id="__EVENTTARGET" value="" />

<input type="hidden" name="__EVENTARGUMENT" id="__EVENTARGUMENT" value="" />

<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="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" />

</div>



<script type="text/javascript">

//<![CDATA[

var theForm = document.forms['NSSrchCorp'];

if (!theForm) {

    theForm = document.NSSrchCorp;

}

function __doPostBack(eventTarget, eventArgument) {

    if (!theForm.onsubmit || (theForm.onsubmit() != false)) {

        theForm.__EVENTTARGET.value = eventTarget;

        theForm.__EVENTARGUMENT.value = eventArgument;

        theForm.submit();

    }

}

//]]>

</script>





<div class="aspNetHidden">



	<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="********" />

	<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="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" />

</div>

						<TABLE id="Table1" cellSpacing="0" cellPadding="0" width="100%" border="0">

							<TR>

								<TD class="title" colSpan="3"><span id="lblTitle"><IMG src='images/titleEn.gif'></span></TD>

							</TR>

							<TR>

								<TD><IMG id="IMG1" alt="" src="images/titLine.jpg" align="left"></TD>

							</TR>

							<TR>

								<TD>

									<TABLE id="Table3" cellSpacing="0" cellPadding="0" width="100%" border="0">

										<TR>

											<TD class="section" width="50%"><font face="Arial, Helvetica, sans-serif" color="#CC0000"> <b><span id="lblSearch">Search by listed corporation - Current Securities</span></TD>

											<TD class="section" align="right" width="50%">&nbsp;<A onclick="javascript:window.open('notes/NSSrchCorp.htm', '')" href="javascript:void(0);" ><IMG alt="" src="images\explannoteEN.gif" border=0 ></A></TD>

										</TR>

									</TABLE>

								</TD>

							</TR>

							<TR>

								<TD vAlign="top" width="770">

									<TABLE id="Table2" cellSpacing="0" cellPadding="0" width="100%" border="0">

										<TR>

											<TD colSpan="4">&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" colSpan="4" height="2"><span id="lblPlease">Please enter the stock code or the name of the listed corporation to search for one of the following types of reports:</span></TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" colSpan="3">

												<ul>

													<li>

														<span id="lblNote4">Complete list of substantial shareholders as of a particular date</span>

													<li>

														<span id="lblNote1">Consolidated list of substantial shareholders as of a particular date</span>

													<li>

														<span id="lblNote2">List of DI notices filed by substantial shareholders or directors who are also substantial shareholders with share interests not less than 5%</span>

													<li>

														<span id="lblNote5">Complete list of directors as of a particular date</span>

													<li>

														<span id="lblNote3">List of DI notices filed by directors</span>

													<li>

														<span id="lblNote6">List of all DI notices</span></li>

												</ul>

											</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top" colSpan="2"><span id="lblErrCriteria" style="color:Red;"></span></TD>

											<TD>&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top" colSpan="2">&nbsp;</TD>

											<TD>&nbsp;</TD>

										</TR>

                                        <TR>

                                            <TD width="5">&nbsp;</TD>

                                            <TD colspan="3">

                                                

                                                <a id="lnkSearchListedCorp" class="SrchCorp" href="javascript:__doPostBack(&#39;lnkSearchListedCorp&#39;,&#39;&#39;)">Search by listed corporation - Delisted Securities</a>

							                </TD>

                                        </TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top" colSpan="2">&nbsp;</TD>

											<TD>&nbsp;</TD>

										</TR>

                                        <TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top" width="100"><span id="lblStockCode">Stock code:</span></TD>

											<TD><input name="txtStockCode" type="text" value="01760" maxlength="5" size="5" id="txtStockCode" style="font-size: 9pt" /></TD>

											<TD>&nbsp;</TD>

										</TR>

										<tr>

                        								<td class="txt" colspan="3">&nbsp;</td>

                    								</tr>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top"><span id="lblLCorp">*Name of listed corporation:</span></TD>

											<TD class="txt"><input name="txtCorpName" type="text" maxlength="150" size="50" id="txtCorpName" style="font-size: 9pt" /><br>

												<A href="NSSelectCorp.aspx?src=MAIN&lang=EN&g_lang=en" >

													Choose from a list of corporations listed on the Exchange

												</A>

												<br>

												<br>

											</TD>

											<TD>&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top"><span id="lblDate">**Date:</span></TD>

											<TD class="txt">

												<TABLE id="Table4" cellSpacing="0" cellPadding="0" width="90%" border="0">

													<TR>

														<TD class="txt" width="200">

                                                            <select name="ddlStartDateDD" id="ddlStartDateDD" style="width:55px;">

	<option value="01">01</option>

	<option value="02">02</option>

	<option value="03">03</option>

	<option value="04">04</option>

	<option value="05">05</option>

	<option value="06">06</option>

	<option value="07">07</option>

	<option value="08">08</option>

	<option value="09">09</option>

	<option value="10">10</option>

	<option value="11">11</option>

	<option value="12">12</option>

	<option value="13">13</option>

	<option value="14">14</option>

	<option value="15">15</option>

	<option value="16">16</option>

	<option value="17">17</option>

	<option value="18">18</option>

	<option value="19">19</option>

	<option value="20">20</option>

	<option value="21">21</option>

	<option value="22">22</option>

	<option value="23">23</option>

	<option value="24">24</option>

	<option value="25">25</option>

	<option selected="selected" value="26">26</option>

	<option value="27">27</option>

	<option value="28">28</option>

	<option value="29">29</option>

	<option value="30">30</option>

	<option value="31">31</option>



</select>&nbsp;

															<select name="ddlStartDateMM" id="ddlStartDateMM" style="width:55px;">

	<option value="01">01</option>

	<option value="02">02</option>

	<option value="03">03</option>

	<option value="04">04</option>

	<option selected="selected" value="05">05</option>

	<option value="06">06</option>

	<option value="07">07</option>

	<option value="08">08</option>

	<option value="09">09</option>

	<option value="10">10</option>

	<option value="11">11</option>

	<option value="12">12</option>



</select>&nbsp;

															<select name="ddlStartDateYYYY" id="ddlStartDateYYYY" style="width:70px;">

	<option value="&lt; 2003">&lt; 2003</option>

	<option value="2003">2003</option>

	<option value="2004">2004</option>

	<option value="2005">2005</option>

	<option value="2006">2006</option>

	<option value="2007">2007</option>

	<option value="2008">2008</option>

	<option value="2009">2009</option>

	<option value="2010">2010</option>

	<option value="2011">2011</option>

	<option value="2012">2012</option>

	<option value="2013">2013</option>

	<option value="2014">2014</option>

	<option value="2015">2015</option>

	<option value="2016">2016</option>

	<option value="2017">2017</option>

	<option value="2018">2018</option>

	<option value="2019">2019</option>

	<option value="2020">2020</option>

	<option value="2021">2021</option>

	<option value="2022">2022</option>

	<option value="2023">2023</option>

	<option selected="selected" value="2024">2024</option>

	<option value="2025">2025</option>



</select>

														</TD>

														<TD class="txt" align="middle" width="40"><span id="lblTo">to </span></TD>

														<TD class="txt" width="400">

                                                            <select name="ddlEndDateDD" id="ddlEndDateDD" style="width:55px;">

	<option value="01">01</option>

	<option value="02">02</option>

	<option value="03">03</option>

	<option value="04">04</option>

	<option value="05">05</option>

	<option value="06">06</option>

	<option value="07">07</option>

	<option value="08">08</option>

	<option value="09">09</option>

	<option value="10">10</option>

	<option value="11">11</option>

	<option value="12">12</option>

	<option value="13">13</option>

	<option value="14">14</option>

	<option value="15">15</option>

	<option value="16">16</option>

	<option value="17">17</option>

	<option value="18">18</option>

	<option value="19">19</option>

	<option value="20">20</option>

	<option value="21">21</option>

	<option value="22">22</option>

	<option value="23">23</option>

	<option value="24">24</option>

	<option value="25">25</option>

	<option selected="selected" value="26">26</option>

	<option value="27">27</option>

	<option value="28">28</option>

	<option value="29">29</option>

	<option value="30">30</option>

	<option value="31">31</option>



</select>&nbsp;

															<select name="ddlEndDateMM" id="ddlEndDateMM" style="width:55px;">

	<option value="01">01</option>

	<option value="02">02</option>

	<option value="03">03</option>

	<option value="04">04</option>

	<option selected="selected" value="05">05</option>

	<option value="06">06</option>

	<option value="07">07</option>

	<option value="08">08</option>

	<option value="09">09</option>

	<option value="10">10</option>

	<option value="11">11</option>

	<option value="12">12</option>



</select>&nbsp;

															<select name="ddlEndDateYYYY" id="ddlEndDateYYYY" style="width:70px;">

	<option value="2003">2003</option>

	<option value="2004">2004</option>

	<option value="2005">2005</option>

	<option value="2006">2006</option>

	<option value="2007">2007</option>

	<option value="2008">2008</option>

	<option value="2009">2009</option>

	<option value="2010">2010</option>

	<option value="2011">2011</option>

	<option value="2012">2012</option>

	<option value="2013">2013</option>

	<option value="2014">2014</option>

	<option value="2015">2015</option>

	<option value="2016">2016</option>

	<option value="2017">2017</option>

	<option value="2018">2018</option>

	<option value="2019">2019</option>

	<option value="2020">2020</option>

	<option value="2021">2021</option>

	<option value="2022">2022</option>

	<option value="2023">2023</option>

	<option value="2024">2024</option>

	<option selected="selected" value="2025">2025</option>



</select>

														</TD>

													<TR>

													<TR>

														<TD class="txt" colSpan="2"><span id="lblFromFormat"> </span></TD>

														<TD class="txt"><span id="lblToFormat"> </span></TD>

													</TR>

													<TR>

														<TD class="txt" colSpan="2"><span id="lblStartDateError" style="color:Red;"></span></TD>

														<TD class="txt"><span id="lblEndDateError" style="color:Red;"></span></TD>

													</TR>

												</TABLE>

											</TD>

											<TD>&nbsp;

											</TD>

										</TR>

										<TR>

											<TD colSpan="5">&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" colSpan="4"><span id="lblRem1">* Partial match of the name of listed corporation requires at least 3 characters to be entered.</span></TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" colSpan="4"><span id="lblRem2">** Enter a period or date depending on the type of report required.</span></TD>

										</TR>

										<TR>

											<TD colSpan="4">&nbsp;</TD>

										</TR>

                                            

										<TR>

											<TD colSpan="4">&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD colSpan="4">

                                                <input type="submit" name="cmdSearch" value="Search" id="cmdSearch" />

                                                <input type="submit" name="cmdBack" value="Back" id="cmdBack" />

											</TD>

										</TR>

										<TR>

											<TD colSpan="5">&nbsp;</TD>

										</TR>

									</TABLE>

								</TD>

							</TR>

							<TR>

								<TD></TD>

							</TR>

						</TABLE>





  

					</form>

					<!-- PCCW Revamped 20040313 -->

					

				</td>

			</tr>

		</table>

		</div>

        </main>

    </div>



    <!-- CMS Control Footer START -->

    <script type="text/javascript" src="/ncms/script/hkex_foot.js"></script>

    <!-- CMS Control Footer END -->

    <!-- NEWS Main Scripts START -->

    <script type="text/javascript" src="/ncms/script/main.js"></script>

    <!-- NEWS Main Scripts END -->

		

		<!-- -->

	</body>

</HTML>

