"""
工具模块 - 香港交易所PDF爬虫
"""

import os
import re
import time
import requests
from urllib.parse import urljoin, urlparse
from tqdm import tqdm
import config


def create_directories():
    """创建下载目录"""
    os.makedirs(config.EN_DIR, exist_ok=True)
    os.makedirs(config.TC_DIR, exist_ok=True)
    print(f"创建下载目录: {config.EN_DIR}, {config.TC_DIR}")


def extract_pdf_id(pdf_url):
    """从PDF URL中提取文件ID，用于配对英文和中文版本"""
    # 例如: 2025032501322_c.pdf -> 2025032501322
    filename = os.path.basename(pdf_url)
    match = re.match(r'(\d+)_[ec]\.pdf', filename)
    if match:
        return match.group(1)
    return None


def is_english_pdf(pdf_url):
    """判断是否为英文PDF"""
    return pdf_url.endswith('_e.pdf')


def is_chinese_pdf(pdf_url):
    """判断是否为繁体中文PDF"""
    return pdf_url.endswith('_c.pdf')


def get_paired_pdf_url(pdf_url):
    """获取配对的PDF URL（英文<->中文）"""
    if is_english_pdf(pdf_url):
        return pdf_url.replace('_e.pdf', '_c.pdf')
    elif is_chinese_pdf(pdf_url):
        return pdf_url.replace('_c.pdf', '_e.pdf')
    return None


def download_file(url, filepath, session=None):
    """下载文件到指定路径"""
    if session is None:
        session = requests.Session()
    
    try:
        response = session.get(url, headers=config.HEADERS, 
                             timeout=config.TIMEOUT, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(filepath, 'wb') as f:
            if total_size > 0:
                with tqdm(total=total_size, unit='B', unit_scale=True, 
                         desc=os.path.basename(filepath)) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            pbar.update(len(chunk))
            else:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
        
        print(f"下载完成: {filepath}")
        return True
        
    except Exception as e:
        print(f"下载失败 {url}: {str(e)}")
        if os.path.exists(filepath):
            os.remove(filepath)
        return False


def retry_request(func, *args, **kwargs):
    """重试机制"""
    for attempt in range(config.MAX_RETRIES):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if attempt == config.MAX_RETRIES - 1:
                raise e
            print(f"请求失败，第 {attempt + 1} 次重试...")
            time.sleep(config.REQUEST_DELAY * (attempt + 1))


def sanitize_filename(filename):
    """清理文件名，移除非法字符"""
    # 移除或替换Windows文件名中的非法字符
    illegal_chars = '<>:"/\\|?*'
    for char in illegal_chars:
        filename = filename.replace(char, '_')
    return filename


def get_file_size_mb(filepath):
    """获取文件大小（MB）"""
    if os.path.exists(filepath):
        size_bytes = os.path.getsize(filepath)
        return size_bytes / (1024 * 1024)
    return 0


def format_url(url, base_url=None):
    """格式化URL，确保是完整的URL"""
    if url.startswith('http'):
        return url
    elif base_url:
        return urljoin(base_url, url)
    else:
        return urljoin(config.BASE_URL, url)
