"""
测试香港交易所PDF爬虫
"""

import os
import requests
from bs4 import BeautifulSoup
import config
import utils
from hkex_pdf_scraper import HKEXPDFScraper


def test_basic_connection():
    """测试基本连接"""
    print("测试基本连接...")
    
    try:
        response = requests.get("https://www1.hkexnews.hk/", 
                              headers=config.HEADERS, 
                              timeout=config.TIMEOUT)
        response.raise_for_status()
        print("✓ 基本连接成功")
        return True
    except Exception as e:
        print(f"✗ 基本连接失败: {str(e)}")
        return False


def test_search_page():
    """测试搜索页面"""
    print("测试搜索页面...")
    
    try:
        response = requests.get(config.SEARCH_URL, 
                              headers=config.HEADERS, 
                              timeout=config.TIMEOUT)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        # 检查是否有搜索表单
        forms = soup.find_all('form')
        if forms:
            print("✓ 搜索页面访问成功，找到搜索表单")
            return True
        else:
            print("✗ 搜索页面没有找到搜索表单")
            return False
            
    except Exception as e:
        print(f"✗ 搜索页面访问失败: {str(e)}")
        return False


def test_pdf_discovery():
    """测试PDF发现功能"""
    print("测试PDF发现功能...")
    
    # 尝试访问一些已知的PDF链接模式
    test_urls = [
        "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/",
        "https://www1.hkexnews.hk/listedco/listconews/sehk/2025/",
    ]
    
    pdf_found = False
    
    for url in test_urls:
        try:
            response = requests.get(url, headers=config.HEADERS, timeout=config.TIMEOUT)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 查找PDF链接
                pdf_links = []
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    if href.endswith('.pdf'):
                        pdf_links.append(href)
                
                if pdf_links:
                    print(f"✓ 在 {url} 找到 {len(pdf_links)} 个PDF链接")
                    # 显示前几个PDF链接作为示例
                    for i, pdf_link in enumerate(pdf_links[:3]):
                        print(f"  示例PDF {i+1}: {pdf_link}")
                    pdf_found = True
                    break
                    
        except Exception as e:
            print(f"访问 {url} 时出错: {str(e)}")
            continue
    
    if not pdf_found:
        print("✗ 没有找到PDF链接")
    
    return pdf_found


def test_pdf_pairing():
    """测试PDF配对功能"""
    print("测试PDF配对功能...")
    
    # 模拟一些PDF链接进行配对测试
    test_pdfs = [
        {
            'url': 'https://www1.hkexnews.hk/test/2025032501322_e.pdf',
            'title': 'Test Document English',
            'filename': '2025032501322_e.pdf'
        },
        {
            'url': 'https://www1.hkexnews.hk/test/2025032501322_c.pdf',
            'title': 'Test Document Chinese',
            'filename': '2025032501322_c.pdf'
        },
        {
            'url': 'https://www1.hkexnews.hk/test/2025032501323_e.pdf',
            'title': 'Another Document English',
            'filename': '2025032501323_e.pdf'
        }
    ]
    
    scraper = HKEXPDFScraper()
    paired_pdfs = scraper.pair_pdfs(test_pdfs)
    
    if paired_pdfs:
        print(f"✓ PDF配对功能正常，找到 {len(paired_pdfs)} 对配对PDF")
        for pair in paired_pdfs:
            print(f"  配对ID: {pair['id']}")
            print(f"    英文: {pair['english']['filename']}")
            print(f"    中文: {pair['chinese']['filename']}")
        return True
    else:
        print("✗ PDF配对功能异常")
        return False


def test_utils_functions():
    """测试工具函数"""
    print("测试工具函数...")
    
    # 测试PDF ID提取
    test_cases = [
        ('2025032501322_e.pdf', '2025032501322'),
        ('2025032501322_c.pdf', '2025032501322'),
        ('invalid.pdf', None)
    ]
    
    all_passed = True
    
    for filename, expected in test_cases:
        result = utils.extract_pdf_id(f"https://example.com/{filename}")
        if result == expected:
            print(f"✓ PDF ID提取测试通过: {filename} -> {result}")
        else:
            print(f"✗ PDF ID提取测试失败: {filename} -> {result} (期望: {expected})")
            all_passed = False
    
    # 测试文件名清理
    dirty_filename = 'test<>:"/\\|?*.pdf'
    clean_filename = utils.sanitize_filename(dirty_filename)
    if '<' not in clean_filename and '>' not in clean_filename:
        print(f"✓ 文件名清理测试通过: {dirty_filename} -> {clean_filename}")
    else:
        print(f"✗ 文件名清理测试失败: {clean_filename}")
        all_passed = False
    
    return all_passed


def run_all_tests():
    """运行所有测试"""
    print("开始运行香港交易所PDF爬虫测试...\n")
    
    tests = [
        ("基本连接测试", test_basic_connection),
        ("搜索页面测试", test_search_page),
        ("PDF发现测试", test_pdf_discovery),
        ("PDF配对测试", test_pdf_pairing),
        ("工具函数测试", test_utils_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 出现异常: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 个测试通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！爬虫准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查网络连接和配置。")


if __name__ == "__main__":
    run_all_tests()
