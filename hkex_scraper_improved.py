"""
香港交易所PDF爬虫 - 改进版本
支持分别搜索英文和繁体中文版本，实现真正的配对下载
"""

import os
import re
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from datetime import datetime
from tqdm import tqdm
import json


class HKEXPDFScraperImproved:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)
        
        # 配置
        self.base_url = "https://www1.hkexnews.hk"
        self.search_url_en = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
        self.search_url_zh = "https://www1.hkexnews.hk/search/titlesearch.xhtml?lang=zh"
        self.download_dir = "downloads"
        self.en_dir = os.path.join(self.download_dir, "english")
        self.tc_dir = os.path.join(self.download_dir, "traditional_chinese")
        self.request_delay = 1
        self.timeout = 30
        
        # 创建下载目录
        os.makedirs(self.en_dir, exist_ok=True)
        os.makedirs(self.tc_dir, exist_ok=True)
    
    def get_search_params(self, from_date, to_date, title_keyword, stock_code):
        """获取搜索参数"""
        try:
            import config
            search_params = config.SEARCH_PARAMS.copy()
            search_params.update({
                'fromDate': from_date,
                'toDate': to_date,
                'title': title_keyword,
                'stockId': stock_code
            })
            return search_params
        except:
            # 默认参数
            return {
                'lang': 'EN',
                'category': '0',
                'market': 'SEHK',
                'stockId': stock_code,
                'documentType': '-1',
                'fromDate': from_date,
                'toDate': to_date,
                'title': title_keyword,
                'searchType': '1'
            }
    
    def search_pdfs_by_language(self, search_url, lang_name, from_date, to_date, 
                               title_keyword, stock_code, max_pages):
        """按语言搜索PDF"""
        print(f"搜索{lang_name}版本PDF...")
        
        search_params = self.get_search_params(from_date, to_date, title_keyword, stock_code)
        pdf_links = []
        
        for page in range(1, max_pages + 1):
            search_params['currentPageNo'] = str(page)
            
            try:
                response = self.session.get(search_url, params=search_params, timeout=self.timeout)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 查找PDF链接
                page_pdfs = []
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    if '.pdf' in href:
                        full_url = href if href.startswith('http') else urljoin(self.base_url, href)
                        page_pdfs.append({
                            'url': full_url,
                            'filename': os.path.basename(href),
                            'title': link.get_text(strip=True),
                            'language': lang_name
                        })
                
                if not page_pdfs:
                    print(f"  {lang_name}第 {page} 页没有找到PDF，停止搜索")
                    break
                
                pdf_links.extend(page_pdfs)
                print(f"  {lang_name}第 {page} 页找到 {len(page_pdfs)} 个PDF")
                
                time.sleep(self.request_delay)
                
            except Exception as e:
                print(f"  搜索{lang_name}第 {page} 页时出错: {str(e)}")
                break
        
        print(f"{lang_name}版本总共找到 {len(pdf_links)} 个PDF")
        return pdf_links
    
    def search_paired_pdfs(self, from_date="01/01/2024", to_date="31/12/2024", 
                          title_keyword="", stock_code="01760", max_pages=10):
        """搜索配对的PDF文件"""
        print("开始搜索配对的PDF文件...")
        print(f"搜索参数: 股票代码={stock_code}, 日期={from_date}到{to_date}")
        
        # 搜索英文版本
        english_pdfs = self.search_pdfs_by_language(
            self.search_url_en, "英文", from_date, to_date, 
            title_keyword, stock_code, max_pages
        )
        
        # 搜索中文版本
        chinese_pdfs = self.search_pdfs_by_language(
            self.search_url_zh, "中文", from_date, to_date, 
            title_keyword, stock_code, max_pages
        )
        
        return english_pdfs, chinese_pdfs
    
    def pair_pdfs_by_position(self, english_pdfs, chinese_pdfs):
        """按位置配对PDF（相同搜索结果的相同位置应该是对应的）"""
        print("按位置配对PDF文件...")
        
        paired_pdfs = []
        min_length = min(len(english_pdfs), len(chinese_pdfs))
        
        for i in range(min_length):
            en_pdf = english_pdfs[i]
            zh_pdf = chinese_pdfs[i]
            
            paired_pdfs.append({
                'id': f"pair_{i+1}",
                'english': en_pdf,
                'chinese': zh_pdf,
                'position': i + 1
            })
        
        print(f"按位置配对成功: {len(paired_pdfs)} 对")
        
        # 显示配对详情
        for pair in paired_pdfs[:5]:  # 显示前5对
            print(f"  配对 {pair['position']}:")
            print(f"    英文: {pair['english']['filename']} - {pair['english']['title'][:50]}")
            print(f"    中文: {pair['chinese']['filename']} - {pair['chinese']['title'][:50]}")
        
        if len(paired_pdfs) > 5:
            print(f"  ... 还有 {len(paired_pdfs) - 5} 对")
        
        return paired_pdfs
    
    def download_file(self, url, filepath):
        """下载文件"""
        try:
            response = self.session.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                if total_size > 0:
                    with tqdm(total=total_size, unit='B', unit_scale=True, 
                             desc=os.path.basename(filepath)) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            
            print(f"下载完成: {filepath}")
            return True
            
        except Exception as e:
            print(f"下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False
    
    def download_pdf_pairs(self, paired_pdfs, download_english=True, download_chinese=True):
        """下载配对的PDF文件"""
        print(f"开始下载 {len(paired_pdfs)} 对PDF文件...")
        
        successful_downloads = 0
        
        for i, pair in enumerate(paired_pdfs, 1):
            print(f"\n处理第 {i}/{len(paired_pdfs)} 对PDF (配对 {pair['position']})")
            
            pair_success = True
            
            # 下载英文版
            if download_english:
                en_filename = f"en_{pair['position']:03d}_{pair['english']['filename']}"
                en_filepath = os.path.join(self.en_dir, en_filename)
                
                if os.path.exists(en_filepath):
                    print(f"英文版已存在: {en_filename}")
                else:
                    print(f"下载英文版: {pair['english']['title'][:50]}...")
                    if not self.download_file(pair['english']['url'], en_filepath):
                        pair_success = False
            
            # 下载中文版
            if download_chinese:
                cn_filename = f"zh_{pair['position']:03d}_{pair['chinese']['filename']}"
                cn_filepath = os.path.join(self.tc_dir, cn_filename)
                
                if os.path.exists(cn_filepath):
                    print(f"中文版已存在: {cn_filename}")
                else:
                    print(f"下载中文版: {pair['chinese']['title'][:50]}...")
                    if not self.download_file(pair['chinese']['url'], cn_filepath):
                        pair_success = False
            
            if pair_success:
                successful_downloads += 1
            
            # 添加延迟
            if i < len(paired_pdfs):
                time.sleep(self.request_delay)
        
        print(f"\n下载完成! 成功下载 {successful_downloads}/{len(paired_pdfs)} 对PDF文件")
        return successful_downloads
    
    def save_results(self, paired_pdfs, filename="paired_results.json"):
        """保存配对结果"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'total_pairs': len(paired_pdfs),
            'pairs': paired_pdfs
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"配对结果已保存到: {filename}")


def main():
    """主函数"""
    print("香港交易所PDF爬虫 - 改进版本")
    print("支持分别搜索英文和繁体中文版本")
    print("=" * 50)
    
    scraper = HKEXPDFScraperImproved()
    
    # 从config.py读取参数或使用默认值
    try:
        import config
        search_params = config.SEARCH_PARAMS
        from_date = search_params.get('fromDate', '01/01/2024')
        to_date = search_params.get('toDate', '31/12/2024')
        stock_code = search_params.get('stockId', '01760')
        title_keyword = search_params.get('title', '')
    except:
        from_date = "01/01/2024"
        to_date = "31/12/2024"
        stock_code = "01760"
        title_keyword = ""
    
    print(f"使用搜索参数:")
    print(f"  股票代码: {stock_code}")
    print(f"  日期范围: {from_date} 到 {to_date}")
    print(f"  关键词: '{title_keyword}' (空表示所有)")
    
    # 搜索PDF文件
    english_pdfs, chinese_pdfs = scraper.search_paired_pdfs(
        from_date=from_date,
        to_date=to_date,
        title_keyword=title_keyword,
        stock_code=stock_code,
        max_pages=10
    )
    
    if not english_pdfs and not chinese_pdfs:
        print("没有找到任何PDF文件")
        return
    
    # 配对PDF
    paired_pdfs = scraper.pair_pdfs_by_position(english_pdfs, chinese_pdfs)
    
    if not paired_pdfs:
        print("没有找到配对的PDF文件")
        return
    
    # 保存结果
    scraper.save_results(paired_pdfs)
    
    # 询问是否下载
    print(f"\n找到 {len(paired_pdfs)} 对配对的PDF文件")
    download = input("\n是否开始下载? (y/n): ").lower().strip()
    
    if download == 'y':
        # 下载PDF
        scraper.download_pdf_pairs(paired_pdfs)
        print("\n🎉 下载完成!")
        print(f"英文PDF保存在: {scraper.en_dir}")
        print(f"中文PDF保存在: {scraper.tc_dir}")
    else:
        print("跳过下载")


if __name__ == "__main__":
    main()
