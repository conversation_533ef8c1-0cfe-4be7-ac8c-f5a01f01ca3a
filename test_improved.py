"""
测试改进版本的爬虫
"""

import requests
from bs4 import BeautifulSoup


def test_dual_language_search():
    """测试双语言搜索"""
    print("测试双语言搜索功能...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # 搜索参数
    search_params = {
        'lang': 'EN',
        'category': '0',
        'market': 'SEHK',
        'stockId': '01760',  # 腾讯
        'documentType': '-1',
        'fromDate': '01/01/2024',
        'toDate': '31/12/2024',
        'title': '',
        'searchType': '1',
        'currentPageNo': '1'
    }
    
    # 测试英文搜索
    print("\n1. 测试英文搜索...")
    en_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
    
    try:
        response = requests.get(en_url, params=search_params, headers=headers, timeout=15)
        print(f"英文搜索状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            en_pdfs = []
            
            for link in soup.find_all('a', href=True):
                href = link['href']
                if '.pdf' in href:
                    en_pdfs.append({
                        'url': href,
                        'filename': href.split('/')[-1],
                        'title': link.get_text(strip=True)
                    })
            
            print(f"英文版找到 {len(en_pdfs)} 个PDF")
            for i, pdf in enumerate(en_pdfs[:3]):
                print(f"  {i+1}. {pdf['filename']} - {pdf['title'][:50]}")
        
    except Exception as e:
        print(f"英文搜索失败: {e}")
        return False
    
    # 测试中文搜索
    print("\n2. 测试中文搜索...")
    zh_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml?lang=zh"
    
    try:
        response = requests.get(zh_url, params=search_params, headers=headers, timeout=15)
        print(f"中文搜索状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            zh_pdfs = []
            
            for link in soup.find_all('a', href=True):
                href = link['href']
                if '.pdf' in href:
                    zh_pdfs.append({
                        'url': href,
                        'filename': href.split('/')[-1],
                        'title': link.get_text(strip=True)
                    })
            
            print(f"中文版找到 {len(zh_pdfs)} 个PDF")
            for i, pdf in enumerate(zh_pdfs[:3]):
                print(f"  {i+1}. {pdf['filename']} - {pdf['title'][:50]}")
        
    except Exception as e:
        print(f"中文搜索失败: {e}")
        return False
    
    # 比较结果
    print(f"\n3. 比较结果...")
    print(f"英文PDF数量: {len(en_pdfs)}")
    print(f"中文PDF数量: {len(zh_pdfs)}")
    
    if len(en_pdfs) > 0 and len(zh_pdfs) > 0:
        print("✓ 双语言搜索都成功找到PDF文件")
        
        # 检查是否有相同数量的结果
        if len(en_pdfs) == len(zh_pdfs):
            print("✓ 英文和中文PDF数量相同，可以进行配对")
        else:
            print(f"⚠️ 英文和中文PDF数量不同，可能需要调整搜索策略")
        
        return True
    else:
        print("✗ 至少有一个语言版本没有找到PDF")
        return False


def test_config_reading():
    """测试配置文件读取"""
    print("\n测试配置文件读取...")
    
    try:
        import config
        search_params = config.SEARCH_PARAMS
        print("✓ 成功读取config.py")
        print(f"  股票代码: {search_params.get('stockId', 'N/A')}")
        print(f"  开始日期: {search_params.get('fromDate', 'N/A')}")
        print(f"  结束日期: {search_params.get('toDate', 'N/A')}")
        return True
    except Exception as e:
        print(f"✗ 读取config.py失败: {e}")
        return False


def main():
    """主测试函数"""
    print("香港交易所PDF爬虫 - 改进版本测试")
    print("=" * 50)
    
    # 测试配置读取
    config_ok = test_config_reading()
    
    # 测试双语言搜索
    search_ok = test_dual_language_search()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"配置读取: {'✓' if config_ok else '✗'}")
    print(f"双语言搜索: {'✓' if search_ok else '✗'}")
    
    if config_ok and search_ok:
        print("\n🎉 改进版本测试通过！可以运行完整程序。")
        print("运行命令: python hkex_scraper_improved.py")
    else:
        print("\n⚠️ 测试未完全通过，请检查网络连接和配置。")


if __name__ == "__main__":
    main()
