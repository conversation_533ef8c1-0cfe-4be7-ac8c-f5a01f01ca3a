"""
调试搜索结果，找出真正的PDF链接
"""

import requests
from bs4 import BeautifulSoup
import re


def debug_search_results():
    """调试搜索结果页面"""
    print("调试香港交易所搜索结果...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # 搜索参数 - 腾讯控股
    search_params = {
        'lang': 'EN',
        'category': '0',
        'market': 'SEHK',
        'stockId': '00700',  # 腾讯控股
        'documentType': '-1',
        'fromDate': '01/01/2024',
        'toDate': '31/12/2024',
        'title': '',
        'searchType': '1',
        'currentPageNo': '1'
    }
    
    # 测试英文搜索
    print("\n=== 英文搜索结果分析 ===")
    en_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
    
    try:
        response = requests.get(en_url, params=search_params, headers=headers, timeout=15)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找所有链接
            all_links = soup.find_all('a', href=True)
            print(f"页面总链接数: {len(all_links)}")
            
            # 分析不同类型的链接
            pdf_links = []
            other_links = []
            
            for link in all_links:
                href = link['href']
                text = link.get_text(strip=True)
                
                if '.pdf' in href.lower():
                    pdf_links.append({
                        'href': href,
                        'text': text,
                        'full_url': href if href.startswith('http') else f"https://www1.hkexnews.hk{href}"
                    })
                elif 'listedco' in href or 'listconews' in href:
                    other_links.append({
                        'href': href,
                        'text': text
                    })
            
            print(f"\n直接PDF链接: {len(pdf_links)}")
            for i, link in enumerate(pdf_links[:5]):
                print(f"  {i+1}. {link['text'][:50]} -> {link['href']}")
            
            print(f"\n上市公司相关链接: {len(other_links)}")
            for i, link in enumerate(other_links[:10]):
                print(f"  {i+1}. {link['text'][:50]} -> {link['href']}")
            
            # 查找表格结构
            tables = soup.find_all('table')
            print(f"\n页面表格数: {len(tables)}")
            
            # 查找可能的结果容器
            result_divs = soup.find_all('div', class_=re.compile(r'result|content|list'))
            print(f"结果容器数: {len(result_divs)}")
            
            # 查找表单
            forms = soup.find_all('form')
            print(f"表单数: {len(forms)}")
            
            # 保存页面内容用于分析
            with open('search_result_en.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("英文搜索结果已保存到 search_result_en.html")
            
    except Exception as e:
        print(f"英文搜索失败: {e}")
    
    # 测试中文搜索
    print("\n=== 中文搜索结果分析 ===")
    zh_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml?lang=zh"
    
    try:
        response = requests.get(zh_url, params=search_params, headers=headers, timeout=15)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找所有链接
            all_links = soup.find_all('a', href=True)
            print(f"页面总链接数: {len(all_links)}")
            
            # 分析不同类型的链接
            pdf_links = []
            other_links = []
            
            for link in all_links:
                href = link['href']
                text = link.get_text(strip=True)
                
                if '.pdf' in href.lower():
                    pdf_links.append({
                        'href': href,
                        'text': text
                    })
                elif 'listedco' in href or 'listconews' in href:
                    other_links.append({
                        'href': href,
                        'text': text
                    })
            
            print(f"\n直接PDF链接: {len(pdf_links)}")
            for i, link in enumerate(pdf_links[:5]):
                print(f"  {i+1}. {link['text'][:50]} -> {link['href']}")
            
            print(f"\n上市公司相关链接: {len(other_links)}")
            for i, link in enumerate(other_links[:10]):
                print(f"  {i+1}. {link['text'][:50]} -> {link['href']}")
            
            # 保存页面内容用于分析
            with open('search_result_zh.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("中文搜索结果已保存到 search_result_zh.html")
            
    except Exception as e:
        print(f"中文搜索失败: {e}")


def analyze_specific_document_page():
    """分析具体的文档页面"""
    print("\n=== 分析具体文档页面 ===")
    
    # 尝试访问一个已知的文档页面
    test_url = "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0318/2024031800455.htm"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(test_url, headers=headers, timeout=15)
        print(f"文档页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找PDF链接
            pdf_links = []
            for link in soup.find_all('a', href=True):
                href = link['href']
                if '.pdf' in href.lower():
                    pdf_links.append({
                        'href': href,
                        'text': link.get_text(strip=True),
                        'full_url': href if href.startswith('http') else f"https://www1.hkexnews.hk{href}"
                    })
            
            print(f"文档页面中的PDF链接: {len(pdf_links)}")
            for link in pdf_links:
                print(f"  {link['text']} -> {link['full_url']}")
                
    except Exception as e:
        print(f"访问文档页面失败: {e}")


def main():
    """主函数"""
    print("香港交易所搜索结果调试工具")
    print("=" * 50)
    
    debug_search_results()
    analyze_specific_document_page()
    
    print("\n" + "=" * 50)
    print("调试完成！")
    print("请查看生成的HTML文件了解页面结构")


if __name__ == "__main__":
    main()
