"""
香港交易所PDF爬虫 - 演示脚本
展示爬虫的基本功能，不进行实际下载
"""

from hkex_scraper_final import HKEXPDFScraper
import j<PERSON>


def demo_search_and_pair():
    """演示搜索和配对功能"""
    print("🚀 香港交易所PDF爬虫演示")
    print("=" * 50)
    
    # 创建爬虫实例
    scraper = HKEXPDFScraper()
    
    print("📋 步骤1: 搜索PDF文件...")
    print("-" * 30)
    
    # 搜索PDF文件
    pdf_links = scraper.find_all_pdfs(
        from_date="01/01/2024",
        to_date="31/12/2024",
        title_keyword="",
        max_pages=2  # 限制页数以加快演示
    )
    
    if not pdf_links:
        print("❌ 没有找到PDF文件")
        return
    
    print(f"\n✅ 找到 {len(pdf_links)} 个PDF文件")
    print("\n前10个PDF文件:")
    for i, pdf in enumerate(pdf_links[:10], 1):
        print(f"  {i:2d}. {pdf['filename']}")
    
    print(f"\n📋 步骤2: 配对英文和中文PDF...")
    print("-" * 30)
    
    # 配对PDF
    paired_pdfs = scraper.pair_pdfs(pdf_links)
    
    if not paired_pdfs:
        print("❌ 没有找到配对的PDF文件")
        return
    
    print(f"\n✅ 找到 {len(paired_pdfs)} 对配对的PDF文件")
    print("\n配对详情:")
    for i, pair in enumerate(paired_pdfs, 1):
        print(f"  {i}. ID: {pair['id']}")
        print(f"     英文: {pair['english']['filename']}")
        print(f"     中文: {pair['chinese']['filename']}")
        print()
    
    print(f"📋 步骤3: 保存结果...")
    print("-" * 30)
    
    # 保存结果
    scraper.save_results(paired_pdfs, "demo_results.json")
    
    print("✅ 结果已保存到 demo_results.json")
    
    print(f"\n📋 步骤4: 显示统计信息...")
    print("-" * 30)
    
    # 统计信息
    english_count = sum(1 for pdf in pdf_links if scraper.is_english_pdf(pdf['url']))
    chinese_count = sum(1 for pdf in pdf_links if scraper.is_chinese_pdf(pdf['url']))
    other_count = len(pdf_links) - english_count - chinese_count
    
    print(f"📊 统计信息:")
    print(f"   总PDF文件数: {len(pdf_links)}")
    print(f"   英文PDF: {english_count}")
    print(f"   中文PDF: {chinese_count}")
    print(f"   其他PDF: {other_count}")
    print(f"   成功配对: {len(paired_pdfs)}")
    print(f"   配对率: {len(paired_pdfs)/max(english_count, chinese_count)*100:.1f}%")
    
    print(f"\n🎉 演示完成!")
    print("=" * 50)
    print("💡 提示:")
    print("   - 运行 'python hkex_scraper_final.py' 开始实际下载")
    print("   - 查看 'demo_results.json' 了解详细结果")
    print("   - 运行 'python simple_test.py' 进行功能测试")


def show_demo_results():
    """显示演示结果"""
    try:
        with open("demo_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print("\n📄 演示结果摘要:")
        print(f"   时间戳: {results['timestamp']}")
        print(f"   配对数量: {results['total_pairs']}")
        
        if results['pairs']:
            print("\n   配对列表:")
            for pair in results['pairs']:
                print(f"     • {pair['id']}: {pair['english']['filename']} ↔ {pair['chinese']['filename']}")
    
    except FileNotFoundError:
        print("❌ 没有找到演示结果文件")
    except Exception as e:
        print(f"❌ 读取演示结果时出错: {str(e)}")


if __name__ == "__main__":
    try:
        demo_search_and_pair()
        show_demo_results()
    except KeyboardInterrupt:
        print("\n\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {str(e)}")
        print("💡 请检查网络连接或运行 'python simple_test.py' 进行诊断")
