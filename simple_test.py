"""
简化测试 - 验证基本功能
"""

import requests
from bs4 import BeautifulSoup
import re
import os


def test_connection():
    """测试网站连接"""
    print("测试连接到香港交易所网站...")

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }

    try:
        response = requests.get(
            "https://www1.hkexnews.hk/", headers=headers, timeout=10
        )
        print(f"连接状态: {response.status_code}")
        if response.status_code == 200:
            print("✓ 网站连接成功")
            return True
        else:
            print("✗ 网站连接失败")
            return False
    except Exception as e:
        print(f"✗ 连接错误: {str(e)}")
        return False


def find_sample_pdfs():
    """查找示例PDF文件"""
    print("\n查找示例PDF文件...")

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }

    found_pdfs = []

    # 方法1: 使用搜索功能
    print("方法1: 使用搜索功能...")
    try:
        search_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
        search_params = {
            "lang": "EN",
            "category": "0",
            "market": "SEHK",
            "stockId": "-1",
            "documentType": "-1",
            "fromDate": "01/01/2024",
            "toDate": "31/12/2024",
            "title": "",
            "searchType": "1",
            "currentPageNo": "1",
        }

        response = requests.get(
            search_url, params=search_params, headers=headers, timeout=15
        )
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, "html.parser")

            # 查找PDF链接
            for link in soup.find_all("a", href=True):
                href = link["href"]
                if ".pdf" in href:
                    full_url = (
                        href
                        if href.startswith("http")
                        else f"https://www1.hkexnews.hk{href}"
                    )
                    found_pdfs.append(
                        {
                            "url": full_url,
                            "filename": os.path.basename(href),
                            "title": link.get_text(strip=True)[:50],
                        }
                    )

                    if len(found_pdfs) >= 10:
                        break

        print(f"搜索功能找到 {len(found_pdfs)} 个PDF")

    except Exception as e:
        print(f"搜索功能失败: {str(e)}")

    # 方法2: 直接使用已知的PDF链接进行测试
    if len(found_pdfs) < 5:
        print("方法2: 使用已知PDF链接...")
        known_pdfs = [
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2025/0318/2025031801045.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0926/2024092600815.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0916/2024091600585.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1118/2024111800749.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0926/2024092600403.pdf",
        ]

        for pdf_url in known_pdfs:
            try:
                # 验证PDF是否存在
                response = requests.head(pdf_url, headers=headers, timeout=10)
                if response.status_code == 200:
                    filename = os.path.basename(pdf_url)
                    found_pdfs.append(
                        {
                            "url": pdf_url,
                            "filename": filename,
                            "title": f"测试PDF - {filename}",
                        }
                    )
                    print(f"验证PDF: {filename} ✓")
                else:
                    print(f"PDF不存在: {os.path.basename(pdf_url)} ✗")
            except Exception as e:
                print(f"验证PDF失败: {os.path.basename(pdf_url)} - {str(e)}")

    # 方法3: 尝试构造配对的PDF链接
    if found_pdfs:
        print("方法3: 查找配对的PDF...")
        additional_pdfs = []

        for pdf in found_pdfs[:3]:  # 只检查前3个
            url = pdf["url"]
            if url.endswith(".pdf"):
                # 尝试找到对应的英文/中文版本
                if "_e.pdf" in url:
                    # 这是英文版，尝试找中文版
                    chinese_url = url.replace("_e.pdf", "_c.pdf")
                elif "_c.pdf" in url:
                    # 这是中文版，尝试找英文版
                    english_url = url.replace("_c.pdf", "_e.pdf")
                else:
                    # 尝试添加语言后缀
                    base_url = url.replace(".pdf", "")
                    chinese_url = f"{base_url}_c.pdf"
                    english_url = f"{base_url}_e.pdf"

                # 检查配对文件是否存在
                for test_url in [chinese_url if "_e.pdf" in url else english_url]:
                    try:
                        response = requests.head(test_url, headers=headers, timeout=5)
                        if response.status_code == 200:
                            additional_pdfs.append(
                                {
                                    "url": test_url,
                                    "filename": os.path.basename(test_url),
                                    "title": f"配对PDF - {os.path.basename(test_url)}",
                                }
                            )
                            print(f"找到配对PDF: {os.path.basename(test_url)} ✓")
                    except:
                        pass

        found_pdfs.extend(additional_pdfs)

    if found_pdfs:
        print(f"\n✓ 总共找到 {len(found_pdfs)} 个PDF文件")
        for i, pdf in enumerate(found_pdfs[:8]):  # 显示前8个
            print(f"  {i+1}. {pdf['filename']} - {pdf['title']}")
        return found_pdfs
    else:
        print("✗ 没有找到PDF文件")
        return []


def test_pdf_pairing(pdfs):
    """测试PDF配对功能"""
    print("\n测试PDF配对功能...")

    def extract_pdf_id(pdf_url):
        """提取PDF ID - 改进版本"""
        filename = os.path.basename(pdf_url)

        # 模式1: 数字ID + 语言后缀 (如: 2025031801045_e.pdf)
        match = re.match(r"(\d+)_[ec]\.pdf", filename)
        if match:
            return match.group(1)

        # 模式2: 文件名 + 语言后缀 (如: SearchDisclaimer_e.pdf)
        match = re.match(r"(.+)_[ec]\.pdf", filename)
        if match:
            return match.group(1)

        # 模式3: 纯数字文件名 (如: 2025031801045.pdf)
        match = re.match(r"(\d+)\.pdf", filename)
        if match:
            return match.group(1)

        return None

    def is_english_pdf(pdf_url):
        return pdf_url.endswith("_e.pdf")

    def is_chinese_pdf(pdf_url):
        return pdf_url.endswith("_c.pdf")

    english_pdfs = {}
    chinese_pdfs = {}

    for pdf in pdfs:
        pdf_id = extract_pdf_id(pdf["url"])
        if not pdf_id:
            continue

        if is_english_pdf(pdf["url"]):
            english_pdfs[pdf_id] = pdf
        elif is_chinese_pdf(pdf["url"]):
            chinese_pdfs[pdf_id] = pdf

    # 找配对
    pairs = []
    for pdf_id in english_pdfs:
        if pdf_id in chinese_pdfs:
            pairs.append(
                {
                    "id": pdf_id,
                    "english": english_pdfs[pdf_id],
                    "chinese": chinese_pdfs[pdf_id],
                }
            )

    print(f"英文PDF: {len(english_pdfs)} 个")
    print(f"中文PDF: {len(chinese_pdfs)} 个")
    print(f"配对PDF: {len(pairs)} 对")

    if pairs:
        print("✓ PDF配对功能正常")
        for pair in pairs[:3]:  # 显示前3对
            print(f"  配对ID {pair['id']}:")
            print(f"    英文: {pair['english']['filename']}")
            print(f"    中文: {pair['chinese']['filename']}")
        return pairs
    else:
        print("✗ 没有找到配对的PDF")
        return []


def test_download_sample(pairs):
    """测试下载示例PDF"""
    if not pairs:
        print("\n跳过下载测试 - 没有配对的PDF")
        return

    print("\n测试下载功能...")

    # 创建测试目录
    os.makedirs("test_downloads", exist_ok=True)

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }

    # 测试下载第一对PDF的前几KB
    test_pair = pairs[0]

    for lang, pdf_info in [
        ("english", test_pair["english"]),
        ("chinese", test_pair["chinese"]),
    ]:
        try:
            print(f"测试下载 {lang} PDF: {pdf_info['filename']}")

            response = requests.get(
                pdf_info["url"], headers=headers, timeout=10, stream=True
            )
            response.raise_for_status()

            # 只下载前1KB来测试
            content = next(response.iter_content(chunk_size=1024))

            if content and content.startswith(b"%PDF"):
                print(f"✓ {lang} PDF 下载测试成功 (有效的PDF文件)")
            else:
                print(f"✗ {lang} PDF 下载测试失败 (不是有效的PDF文件)")

        except Exception as e:
            print(f"✗ {lang} PDF 下载测试失败: {str(e)}")


def main():
    """主测试函数"""
    print("香港交易所PDF爬虫 - 简化测试")
    print("=" * 50)

    # 测试连接
    if not test_connection():
        print("网站连接失败，无法继续测试")
        return

    # 查找PDF
    pdfs = find_sample_pdfs()
    if not pdfs:
        print("没有找到PDF文件，无法继续测试")
        return

    # 测试配对
    pairs = test_pdf_pairing(pdfs)

    # 测试下载
    test_download_sample(pairs)

    print("\n" + "=" * 50)
    print("测试完成!")

    if pairs:
        print("🎉 爬虫基本功能正常，可以开始使用!")
    else:
        print("⚠️ 没有找到配对的PDF，可能需要调整搜索策略")


if __name__ == "__main__":
    main()
