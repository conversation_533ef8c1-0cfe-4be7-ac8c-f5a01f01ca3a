"""
测试修复版本 - 验证是否能获取真正的披露报告
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin


def test_document_extraction():
    """测试文档提取功能"""
    print("测试文档提取功能...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # 搜索腾讯控股的公告
    search_params = {
        'lang': 'EN',
        'category': '0',
        'market': 'SEHK',
        'stockId': '00700',  # 腾讯控股
        'documentType': '-1',
        'fromDate': '01/01/2024',
        'toDate': '31/12/2024',
        'title': '',
        'searchType': '1',
        'currentPageNo': '1'
    }
    
    print("1. 搜索腾讯控股的公告...")
    en_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
    
    try:
        response = requests.get(en_url, params=search_params, headers=headers, timeout=15)
        print(f"搜索状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找文档链接
            document_links = []
            
            # 查找所有链接
            all_links = soup.find_all('a', href=True)
            for link in all_links:
                href = link['href']
                text = link.get_text(strip=True)
                
                # 查找指向文档页面的链接
                if ('listedco' in href and 'listconews' in href and 
                    ('.htm' in href or '.html' in href) and
                    len(text) > 10):
                    
                    full_url = href if href.startswith('http') else urljoin("https://www1.hkexnews.hk", href)
                    document_links.append({
                        'url': full_url,
                        'title': text
                    })
            
            print(f"找到 {len(document_links)} 个文档链接")
            
            # 显示前几个文档
            for i, doc in enumerate(document_links[:5]):
                print(f"  {i+1}. {doc['title']}")
                print(f"      {doc['url']}")
            
            # 测试提取第一个文档的PDF
            if document_links:
                print(f"\n2. 测试提取第一个文档的PDF...")
                test_doc = document_links[0]
                print(f"测试文档: {test_doc['title']}")
                
                try:
                    doc_response = requests.get(test_doc['url'], headers=headers, timeout=15)
                    if doc_response.status_code == 200:
                        doc_soup = BeautifulSoup(doc_response.content, 'html.parser')
                        
                        # 查找PDF链接
                        pdf_links = []
                        for link in doc_soup.find_all('a', href=True):
                            href = link['href']
                            link_text = link.get_text(strip=True)
                            
                            if '.pdf' in href.lower():
                                full_url = href if href.startswith('http') else urljoin("https://www1.hkexnews.hk", href)
                                
                                # 过滤掉免责声明等
                                if ('disclaimer' not in href.lower() and 
                                    'terms' not in href.lower()):
                                    
                                    pdf_links.append({
                                        'url': full_url,
                                        'filename': href.split('/')[-1],
                                        'text': link_text
                                    })
                        
                        print(f"在文档中找到 {len(pdf_links)} 个PDF链接:")
                        for pdf in pdf_links:
                            print(f"  - {pdf['filename']} ({pdf['text']})")
                            print(f"    {pdf['url']}")
                        
                        if pdf_links:
                            print("✓ 成功找到真正的PDF文件!")
                            return True
                        else:
                            print("✗ 文档中没有找到PDF文件")
                            return False
                    
                except Exception as e:
                    print(f"访问文档页面失败: {e}")
                    return False
            else:
                print("✗ 没有找到文档链接")
                return False
        
    except Exception as e:
        print(f"搜索失败: {e}")
        return False


def test_specific_company():
    """测试特定公司的搜索"""
    print("\n" + "="*50)
    print("测试特定公司搜索 - 中国移动 (00941)")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    search_params = {
        'lang': 'EN',
        'category': '0',
        'market': 'SEHK',
        'stockId': '00941',  # 中国移动
        'documentType': '-1',
        'fromDate': '01/01/2024',
        'toDate': '31/12/2024',
        'title': '',
        'searchType': '1',
        'currentPageNo': '1'
    }
    
    en_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
    
    try:
        response = requests.get(en_url, params=search_params, headers=headers, timeout=15)
        print(f"搜索状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找文档链接
            document_count = 0
            for link in soup.find_all('a', href=True):
                href = link['href']
                text = link.get_text(strip=True)
                
                if ('listedco' in href and 'listconews' in href and 
                    ('.htm' in href or '.html' in href) and
                    len(text) > 10):
                    document_count += 1
                    
                    if document_count <= 3:  # 只显示前3个
                        print(f"  {document_count}. {text}")
            
            print(f"中国移动找到 {document_count} 个文档")
            return document_count > 0
        
    except Exception as e:
        print(f"搜索中国移动失败: {e}")
        return False


def main():
    """主测试函数"""
    print("香港交易所PDF爬虫 - 修复版本测试")
    print("验证是否能获取真正的披露报告")
    print("=" * 50)
    
    # 测试文档提取
    test1_result = test_document_extraction()
    
    # 测试特定公司
    test2_result = test_specific_company()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"文档提取测试: {'✓ 通过' if test1_result else '✗ 失败'}")
    print(f"特定公司测试: {'✓ 通过' if test2_result else '✗ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 修复版本测试通过！")
        print("现在应该能获取真正的上市公司披露报告了。")
        print("运行命令: python hkex_scraper_fixed.py")
    else:
        print("\n⚠️ 测试未完全通过，可能需要进一步调整。")


if __name__ == "__main__":
    main()
