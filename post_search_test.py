"""
POST搜索测试 - 尝试使用POST方法提交搜索
"""

import requests
from bs4 import BeautifulSoup
import time


def test_post_search():
    """测试POST搜索"""
    print("测试POST搜索方法...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Referer': 'https://www1.hkexnews.hk/',
    })
    
    try:
        # 第1步：获取搜索页面和必要的表单数据
        print("第1步：获取搜索页面...")
        search_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
        response = session.get(search_url, timeout=30)
        print(f"搜索页面状态码: {response.status_code}")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找ViewState等隐藏字段
        viewstate = soup.find('input', {'name': 'javax.faces.ViewState'})
        viewstate_value = viewstate['value'] if viewstate else None
        
        # 查找表单
        form = soup.find('form')
        if form:
            action = form.get('action', search_url)
            print(f"表单action: {action}")
        
        # 第2步：尝试POST搜索
        print("第2步：尝试POST搜索...")
        
        # 构建POST数据
        post_data = {
            'stockId': '01760',
            'fromDate': '01/01/2024',
            'toDate': '31/12/2024',
            'searchType': '1',
            'category': '0',
            'market': 'SEHK',
            'documentType': '-1',
            'title': '',
            'lang': 'EN'
        }
        
        if viewstate_value:
            post_data['javax.faces.ViewState'] = viewstate_value
        
        print(f"POST数据: {post_data}")
        
        # 发送POST请求
        response = session.post(search_url, data=post_data, timeout=30)
        print(f"POST响应状态码: {response.status_code}")
        print(f"POST响应URL: {response.url}")
        
        # 保存响应
        with open('post_search_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("POST响应已保存到 post_search_response.html")
        
        # 分析响应
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找记录统计
        total_records = soup.find(string=lambda t: t and 'Total records found' in str(t))
        if total_records:
            print(f"POST搜索结果: {total_records.strip()}")
        
        # 第3步：尝试不同的URL
        print("\n第3步：尝试不同的搜索URL...")
        
        alternative_urls = [
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1212/2024121200741.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/",
            "https://www1.hkexnews.hk/search/",
            "https://www1.hkexnews.hk/search/titlesearch.xhtml?lang=zh",
        ]
        
        for url in alternative_urls:
            try:
                print(f"尝试访问: {url}")
                response = session.get(url, timeout=30)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    filename = f"test_{url.split('/')[-1] or 'index'}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"已保存到: {filename}")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"访问 {url} 失败: {e}")
        
        # 第4步：尝试直接构造已知的PDF URL
        print("\n第4步：尝试直接访问已知PDF...")
        
        # 从您的截图中，我看到有Monthly Returns等文档
        # 让我们尝试构造一些可能的URL
        possible_pdfs = [
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1205/2024120500741.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1129/2024112900741.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1106/2024110600741.pdf",
        ]
        
        for pdf_url in possible_pdfs:
            try:
                print(f"尝试访问PDF: {pdf_url}")
                response = session.head(pdf_url, timeout=10)  # 只获取头部信息
                print(f"PDF状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"✓ PDF存在: {pdf_url}")
                    # 可以下载这个PDF作为测试
                    
            except Exception as e:
                print(f"PDF访问失败: {e}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_post_search()
