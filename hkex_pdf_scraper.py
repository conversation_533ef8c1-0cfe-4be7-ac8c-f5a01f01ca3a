"""
香港交易所PDF爬虫
爬取 https://www.hkexnews.hk/ 中的PDF文件（英文和繁体中文对应版本）
"""

import os
import re
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from datetime import datetime, timedelta
import config
import utils


class HKEXPDFScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(config.HEADERS)
        self.pdf_pairs = {}  # 存储PDF配对信息

    def search_documents(
        self, from_date=None, to_date=None, title_keyword="", max_pages=5
    ):
        """搜索文档列表"""
        print("开始搜索文档...")

        # 如果没有指定日期，默认搜索最近30天
        if not from_date:
            to_date = datetime.now()
            from_date = to_date - timedelta(days=30)
            from_date = from_date.strftime("%d/%m/%Y")
            to_date = to_date.strftime("%d/%m/%Y")

        pdf_links = []

        # 尝试多种搜索方式
        search_methods = [
            self._search_by_title,
            self._search_by_browse,
            self._search_by_direct_links,
        ]

        for method in search_methods:
            try:
                method_links = method(from_date, to_date, title_keyword, max_pages)
                pdf_links.extend(method_links)
                if method_links:
                    print(f"通过 {method.__name__} 找到 {len(method_links)} 个PDF链接")
            except Exception as e:
                print(f"搜索方法 {method.__name__} 失败: {str(e)}")
                continue

        # 去重
        unique_links = {}
        for link in pdf_links:
            unique_links[link["url"]] = link
        pdf_links = list(unique_links.values())

        print(f"总共找到 {len(pdf_links)} 个唯一PDF链接")
        return pdf_links

    def _search_by_title(self, from_date, to_date, title_keyword, max_pages):
        """通过标题搜索"""
        search_params = config.SEARCH_PARAMS.copy()
        search_params.update(
            {"fromDate": from_date, "toDate": to_date, "title": title_keyword}
        )

        pdf_links = []

        for page in range(1, max_pages + 1):
            search_params["currentPageNo"] = str(page)

            response = utils.retry_request(
                self.session.get,
                config.SEARCH_URL,
                params=search_params,
                timeout=config.TIMEOUT,
            )
            response.raise_for_status()

            soup = BeautifulSoup(response.content, "html.parser")
            page_links = self._extract_pdf_links(soup)

            if not page_links:
                break

            pdf_links.extend(page_links)
            time.sleep(config.REQUEST_DELAY)

        return pdf_links

    def _search_by_browse(self, from_date, to_date, title_keyword, max_pages):
        """通过浏览方式搜索"""
        # 尝试访问不同的浏览页面
        browse_urls = [
            "https://www1.hkexnews.hk/listedco/listconews/sehk/",
            "https://www1.hkexnews.hk/listedco/listconews/gem/",
        ]

        pdf_links = []

        for base_url in browse_urls:
            try:
                # 获取年份和月份的目录
                current_year = datetime.now().year
                for year in range(current_year - 1, current_year + 1):
                    year_url = f"{base_url}{year}/"
                    year_links = self._crawl_directory_links(year_url)
                    pdf_links.extend(year_links)

            except Exception as e:
                print(f"浏览 {base_url} 时出错: {str(e)}")
                continue

        return pdf_links

    def _search_by_direct_links(self, from_date, to_date, title_keyword, max_pages):
        """通过直接链接模式搜索"""
        pdf_links = []

        # 基于观察到的URL模式生成可能的链接
        current_date = datetime.now()

        for days_back in range(0, 90):  # 搜索过去90天
            date = current_date - timedelta(days=days_back)
            date_str = date.strftime("%Y/%m%d")

            # 尝试不同的URL模式
            patterns = [
                f"https://www1.hkexnews.hk/listedco/listconews/sehk/{date_str}/",
                f"https://www1.hkexnews.hk/listedco/listconews/gem/{date_str}/",
            ]

            for pattern in patterns:
                try:
                    links = self._crawl_directory_links(pattern)
                    pdf_links.extend(links)
                except:
                    continue

        return pdf_links

    def _crawl_directory_links(self, url):
        """爬取目录中的PDF链接"""
        pdf_links = []

        try:
            response = self.session.get(
                url, headers=config.HEADERS, timeout=config.TIMEOUT
            )
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, "html.parser")
                links = self._extract_pdf_links(soup)
                pdf_links.extend(links)
        except:
            pass

        return pdf_links

    def _extract_pdf_links(self, soup):
        """从页面中提取PDF链接"""
        pdf_links = []

        # 查找所有PDF链接
        for link in soup.find_all("a", href=True):
            href = link["href"]
            if href.endswith(".pdf"):
                full_url = utils.format_url(href, config.BASE_URL)
                pdf_links.append(
                    {
                        "url": full_url,
                        "title": link.get_text(strip=True),
                        "filename": os.path.basename(urlparse(href).path),
                    }
                )

        return pdf_links

    def pair_pdfs(self, pdf_links):
        """配对英文和中文PDF"""
        print("正在配对英文和中文PDF...")

        english_pdfs = {}
        chinese_pdfs = {}

        for pdf in pdf_links:
            pdf_id = utils.extract_pdf_id(pdf["url"])
            if not pdf_id:
                continue

            if utils.is_english_pdf(pdf["url"]):
                english_pdfs[pdf_id] = pdf
            elif utils.is_chinese_pdf(pdf["url"]):
                chinese_pdfs[pdf_id] = pdf

        # 找到配对的PDF
        paired_pdfs = []
        for pdf_id in english_pdfs:
            if pdf_id in chinese_pdfs:
                paired_pdfs.append(
                    {
                        "id": pdf_id,
                        "english": english_pdfs[pdf_id],
                        "chinese": chinese_pdfs[pdf_id],
                    }
                )

        print(f"找到 {len(paired_pdfs)} 对配对的PDF文件")
        return paired_pdfs

    def download_pdf_pairs(
        self, paired_pdfs, download_english=True, download_chinese=True
    ):
        """下载配对的PDF文件"""
        utils.create_directories()

        total_pairs = len(paired_pdfs)
        successful_downloads = 0

        for i, pair in enumerate(paired_pdfs, 1):
            print(f"\n处理第 {i}/{total_pairs} 对PDF文件 (ID: {pair['id']})")

            pair_success = True

            # 下载英文版
            if download_english:
                en_filename = utils.sanitize_filename(pair["english"]["filename"])
                en_filepath = os.path.join(config.EN_DIR, en_filename)

                if os.path.exists(en_filepath):
                    print(f"英文版已存在: {en_filename}")
                else:
                    print(f"下载英文版: {pair['english']['title']}")
                    if not utils.download_file(
                        pair["english"]["url"], en_filepath, self.session
                    ):
                        pair_success = False

            # 下载中文版
            if download_chinese:
                cn_filename = utils.sanitize_filename(pair["chinese"]["filename"])
                cn_filepath = os.path.join(config.TC_DIR, cn_filename)

                if os.path.exists(cn_filepath):
                    print(f"中文版已存在: {cn_filename}")
                else:
                    print(f"下载中文版: {pair['chinese']['title']}")
                    if not utils.download_file(
                        pair["chinese"]["url"], cn_filepath, self.session
                    ):
                        pair_success = False

            if pair_success:
                successful_downloads += 1

            # 添加延迟避免过于频繁的请求
            if i < total_pairs:
                time.sleep(config.REQUEST_DELAY)

        print(f"\n下载完成! 成功下载 {successful_downloads}/{total_pairs} 对PDF文件")
        return successful_downloads


def main():
    """主函数"""
    print("香港交易所PDF爬虫启动...")

    scraper = HKEXPDFScraper()

    # 搜索文档（可以修改这些参数）
    pdf_links = scraper.search_documents(
        from_date="01/01/2024",  # 开始日期
        to_date="31/12/2024",  # 结束日期
        title_keyword="",  # 标题关键词（留空表示所有）
        max_pages=3,  # 最大搜索页数
    )

    if not pdf_links:
        print("没有找到PDF文件")
        return

    # 配对PDF
    paired_pdfs = scraper.pair_pdfs(pdf_links)

    if not paired_pdfs:
        print("没有找到配对的PDF文件")
        return

    # 下载PDF
    scraper.download_pdf_pairs(
        paired_pdfs,
        download_english=True,  # 是否下载英文版
        download_chinese=True,  # 是否下载中文版
    )


if __name__ == "__main__":
    main()
