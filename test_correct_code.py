"""
测试正确的股票代码 209176
"""

import requests
from datetime import datetime


def test_intron_tech_code():
    """测试Intron Technology Holdings Ltd.的正确股票代码"""
    print("测试Intron Technology Holdings Ltd.的股票代码...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # 从HTML中发现的正确代码
    correct_code = "209176"
    
    # 测试日期
    test_dates = [
        ("2024/1129", "20241129"),
        ("2024/1106", "20241106"),
        ("2024/1205", "20241205"),
    ]
    
    base_url = "https://www1.hkexnews.hk/listedco/listconews/sehk"
    
    print(f"测试股票代码: {correct_code} (Intron Technology Holdings Ltd.)")
    
    found_urls = []
    
    for date_path, date_str in test_dates:
        print(f"\n测试日期: {date_path}")
        
        # 测试不同的URL格式
        test_urls = [
            f"{base_url}/{date_path}/{date_str}{correct_code}.pdf",
            f"{base_url}/{date_path}/{date_str}{correct_code}_e.pdf",
            f"{base_url}/{date_path}/{date_str}{correct_code}_c.pdf",
            # 也测试5位格式
            f"{base_url}/{date_path}/{date_str}{correct_code.zfill(5)}.pdf",
            f"{base_url}/{date_path}/{date_str}{correct_code.zfill(5)}_e.pdf",
            f"{base_url}/{date_path}/{date_str}{correct_code.zfill(5)}_c.pdf",
        ]
        
        for url in test_urls:
            try:
                response = session.head(url, timeout=10)
                if response.status_code == 200:
                    print(f"  ✓ 存在: {url}")
                    found_urls.append(url)
                else:
                    print(f"  ✗ {response.status_code}: {url}")
                    
            except Exception as e:
                print(f"  ✗ 错误: {url}")
    
    print(f"\n总结:")
    print(f"股票代码 {correct_code} 测试结果:")
    if found_urls:
        print(f"✓ 找到 {len(found_urls)} 个有效PDF:")
        for url in found_urls:
            print(f"  {url}")
        
        # 验证这确实是Intron Technology的文档
        print(f"\n建议:")
        print(f"1. 下载其中一个PDF文件验证公司名称")
        print(f"2. 如果确认是Intron Technology，则正确的股票代码映射是:")
        print(f"   显示代码: 01760")
        print(f"   PDF代码: {correct_code}")
        
    else:
        print(f"✗ 没有找到有效的PDF文件")
        print(f"可能的原因:")
        print(f"1. {correct_code} 不是PDF文件中使用的代码")
        print(f"2. 需要其他格式的代码")
        print(f"3. Intron Technology可能没有在这些日期发布文档")


if __name__ == "__main__":
    test_intron_tech_code()
