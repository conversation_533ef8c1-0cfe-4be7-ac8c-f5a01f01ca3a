"""
香港交易所PDF爬虫 - 表格解析版本
专门解析搜索结果表格，获取所有33个文档
"""

import os
import re
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from datetime import datetime
from tqdm import tqdm
import json


class HKEXTableScraper:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Referer": "https://www1.hkexnews.hk/",
        }
        self.session.headers.update(self.headers)

        # 配置
        self.base_url = "https://www1.hkexnews.hk"
        self.search_url_en = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
        self.search_url_zh = "https://www1.hkexnews.hk/search/titlesearch.xhtml?lang=zh"
        self.download_dir = "downloads"
        self.en_dir = os.path.join(self.download_dir, "english")
        self.tc_dir = os.path.join(self.download_dir, "traditional_chinese")
        self.request_delay = 2
        self.timeout = 30

        # 创建下载目录
        os.makedirs(self.en_dir, exist_ok=True)
        os.makedirs(self.tc_dir, exist_ok=True)

    def get_search_params(self, from_date, to_date, title_keyword, stock_code):
        """获取搜索参数"""
        try:
            import config

            search_params = config.SEARCH_PARAMS.copy()
            search_params.update(
                {
                    "fromDate": from_date,
                    "toDate": to_date,
                    "title": title_keyword,
                    "stockId": stock_code,
                }
            )
            return search_params
        except:
            return {
                "lang": "EN",
                "category": "0",
                "market": "SEHK",
                "stockId": stock_code,
                "documentType": "-1",
                "fromDate": from_date,
                "toDate": to_date,
                "title": title_keyword,
                "searchType": "1",
            }

    def parse_search_results_table(self, soup, language):
        """解析搜索结果表格"""
        documents = []

        # 查找结果表格
        tables = soup.find_all("table")
        print(f"  找到 {len(tables)} 个表格")

        for table_idx, table in enumerate(tables):
            # 查找表头，确认这是结果表格
            headers = table.find_all("th")
            header_texts = [th.get_text(strip=True) for th in headers]

            print(f"  表格 {table_idx + 1} 列标题: {header_texts}")

            # 检查是否包含预期的列
            if any(
                "Document" in header
                or "Release Time" in header
                or "Stock Code" in header
                for header in header_texts
            ):
                print(f"  找到结果表格，列标题: {header_texts}")

                # 解析表格行
                rows = table.find_all("tr")
                print(f"  表格共有 {len(rows)} 行")

                for i, row in enumerate(rows):
                    if i == 0:  # 跳过表头
                        continue

                    cells = row.find_all(["td", "th"])
                    print(f"    第{i}行有 {len(cells)} 列")

                    if len(cells) >= 3:  # 至少需要3列
                        try:
                            # 提取各列信息
                            release_time = (
                                cells[0].get_text(strip=True) if len(cells) > 0 else ""
                            )
                            stock_code = (
                                cells[1].get_text(strip=True) if len(cells) > 1 else ""
                            )
                            stock_name = (
                                cells[2].get_text(strip=True) if len(cells) > 2 else ""
                            )
                            document_cell = (
                                cells[3] if len(cells) > 3 else cells[-1]
                            )  # 使用最后一列作为文档列

                            print(
                                f"    第{i}行内容: 时间={release_time}, 代码={stock_code}, 名称={stock_name}"
                            )

                            if document_cell:
                                # 在Document列中查找链接
                                links = document_cell.find_all("a", href=True)
                                print(f"    第{i}行找到 {len(links)} 个链接")

                                for link_idx, link in enumerate(links):
                                    href = link["href"]
                                    link_text = link.get_text(strip=True)

                                    print(
                                        f"      链接{link_idx + 1}: {href} -> {link_text[:30]}"
                                    )

                                    # 查找PDF链接或文档页面链接
                                    if (
                                        ".pdf" in href.lower()
                                        or "listedco" in href
                                        or "listconews" in href
                                        or "search" in href
                                        or href.startswith("/")
                                    ):  # 相对链接也可能是文档

                                        full_url = (
                                            href
                                            if href.startswith("http")
                                            else urljoin(self.base_url, href)
                                        )

                                        documents.append(
                                            {
                                                "url": full_url,
                                                "title": link_text,
                                                "release_time": release_time,
                                                "stock_code": stock_code,
                                                "stock_name": stock_name,
                                                "language": language,
                                                "row_index": i,
                                                "is_pdf": ".pdf" in href.lower(),
                                            }
                                        )

                                        print(
                                            f"    ✓ 第{i}行: {link_text[:50]} -> {'PDF' if '.pdf' in href.lower() else 'DOC'}"
                                        )
                                    else:
                                        print(f"    ✗ 跳过链接: {href}")

                        except Exception as e:
                            print(f"    解析第{i}行时出错: {str(e)}")
                            continue
                    else:
                        print(f"    第{i}行列数不足，跳过")
            else:
                print(f"  表格 {table_idx + 1} 不是结果表格，跳过")

        return documents

    def extract_pdfs_from_document_page(self, doc_info):
        """从文档页面提取PDF链接"""
        if doc_info["is_pdf"]:
            # 如果已经是PDF链接，直接返回
            return [
                {
                    "url": doc_info["url"],
                    "filename": os.path.basename(doc_info["url"]),
                    "title": doc_info["title"],
                    "release_time": doc_info["release_time"],
                    "stock_code": doc_info["stock_code"],
                    "language": doc_info["language"],
                }
            ]

        # 如果是文档页面，需要进一步提取PDF
        try:
            print(f"    访问文档页面: {doc_info['title'][:30]}...")
            response = self.session.get(doc_info["url"], timeout=self.timeout)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, "html.parser")
            pdf_links = []

            # 查找PDF链接
            for link in soup.find_all("a", href=True):
                href = link["href"]
                link_text = link.get_text(strip=True)

                if ".pdf" in href.lower():
                    full_url = (
                        href
                        if href.startswith("http")
                        else urljoin(self.base_url, href)
                    )

                    # 过滤掉免责声明等
                    if (
                        "disclaimer" not in href.lower()
                        and "terms" not in href.lower()
                        and "search" not in href.lower()
                    ):

                        pdf_links.append(
                            {
                                "url": full_url,
                                "filename": os.path.basename(href),
                                "title": link_text if link_text else doc_info["title"],
                                "release_time": doc_info["release_time"],
                                "stock_code": doc_info["stock_code"],
                                "language": doc_info["language"],
                                "source_doc": doc_info["title"],
                            }
                        )

            return pdf_links

        except Exception as e:
            print(f"    提取PDF失败: {str(e)}")
            return []

    def search_documents_by_language(
        self,
        search_url,
        lang_name,
        from_date,
        to_date,
        title_keyword,
        stock_code,
        max_pages,
    ):
        """按语言搜索文档"""
        print(f"\n搜索{lang_name}版本文档...")

        search_params = self.get_search_params(
            from_date, to_date, title_keyword, stock_code
        )
        all_documents = []
        all_pdfs = []

        for page in range(1, max_pages + 1):
            search_params["currentPageNo"] = str(page)

            try:
                print(f"  正在搜索{lang_name}第 {page} 页...")
                response = self.session.get(
                    search_url, params=search_params, timeout=self.timeout
                )
                response.raise_for_status()

                soup = BeautifulSoup(response.content, "html.parser")

                # 解析表格
                page_documents = self.parse_search_results_table(soup, lang_name)

                if not page_documents:
                    print(f"  {lang_name}第 {page} 页没有找到文档，停止搜索")
                    break

                all_documents.extend(page_documents)
                print(f"  {lang_name}第 {page} 页找到 {len(page_documents)} 个文档")

                # 从每个文档提取PDF
                for doc in page_documents:
                    pdfs = self.extract_pdfs_from_document_page(doc)
                    all_pdfs.extend(pdfs)
                    time.sleep(0.5)  # 小延迟

                time.sleep(self.request_delay)

            except Exception as e:
                print(f"  搜索{lang_name}第 {page} 页时出错: {str(e)}")
                break

        print(f"{lang_name}版本总共找到:")
        print(f"  文档: {len(all_documents)} 个")
        print(f"  PDF: {len(all_pdfs)} 个")

        return all_documents, all_pdfs

    def search_paired_documents(
        self,
        from_date="01/01/2024",
        to_date="31/12/2024",
        title_keyword="",
        stock_code="01760",
        max_pages=10,
    ):
        """搜索配对的文档"""
        print("开始搜索配对的文档...")
        print(f"搜索参数: 股票代码={stock_code}, 日期={from_date}到{to_date}")

        # 搜索英文版本
        en_docs, en_pdfs = self.search_documents_by_language(
            self.search_url_en,
            "英文",
            from_date,
            to_date,
            title_keyword,
            stock_code,
            max_pages,
        )

        # 搜索中文版本
        zh_docs, zh_pdfs = self.search_documents_by_language(
            self.search_url_zh,
            "中文",
            from_date,
            to_date,
            title_keyword,
            stock_code,
            max_pages,
        )

        return en_docs, en_pdfs, zh_docs, zh_pdfs

    def pair_pdfs_by_position(self, en_pdfs, zh_pdfs):
        """按位置配对PDF"""
        print("\n按位置配对PDF...")

        paired_pdfs = []
        min_length = min(len(en_pdfs), len(zh_pdfs))

        for i in range(min_length):
            paired_pdfs.append(
                {
                    "id": f"pair_{i+1}",
                    "english": en_pdfs[i],
                    "chinese": zh_pdfs[i],
                    "position": i + 1,
                }
            )

        print(f"按位置配对成功: {len(paired_pdfs)} 对")

        # 显示配对详情
        for pair in paired_pdfs[:5]:
            print(f"  配对 {pair['position']}:")
            print(
                f"    英文: {pair['english']['filename']} - {pair['english']['title'][:40]}"
            )
            print(
                f"    中文: {pair['chinese']['filename']} - {pair['chinese']['title'][:40]}"
            )

        if len(paired_pdfs) > 5:
            print(f"  ... 还有 {len(paired_pdfs) - 5} 对")

        return paired_pdfs

    def download_file(self, url, filepath):
        """下载文件"""
        try:
            response = self.session.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()

            total_size = int(response.headers.get("content-length", 0))

            with open(filepath, "wb") as f:
                if total_size > 0:
                    with tqdm(
                        total=total_size,
                        unit="B",
                        unit_scale=True,
                        desc=os.path.basename(filepath),
                    ) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

            print(f"下载完成: {filepath}")
            return True

        except Exception as e:
            print(f"下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False

    def download_pdf_pairs(
        self, paired_pdfs, download_english=True, download_chinese=True
    ):
        """下载配对的PDF文件"""
        print(f"\n开始下载 {len(paired_pdfs)} 对PDF文件...")

        successful_downloads = 0

        for i, pair in enumerate(paired_pdfs, 1):
            print(f"\n处理第 {i}/{len(paired_pdfs)} 对PDF (配对 {pair['position']})")

            pair_success = True

            # 下载英文版
            if download_english:
                en_pdf = pair["english"]
                en_filename = f"en_{pair['position']:03d}_{en_pdf['filename']}"
                en_filepath = os.path.join(self.en_dir, en_filename)

                if os.path.exists(en_filepath):
                    print(f"英文版已存在: {en_filename}")
                else:
                    print(f"下载英文版: {en_pdf['title'][:50]}...")
                    if not self.download_file(en_pdf["url"], en_filepath):
                        pair_success = False

            # 下载中文版
            if download_chinese:
                zh_pdf = pair["chinese"]
                zh_filename = f"zh_{pair['position']:03d}_{zh_pdf['filename']}"
                zh_filepath = os.path.join(self.tc_dir, zh_filename)

                if os.path.exists(zh_filepath):
                    print(f"中文版已存在: {zh_filename}")
                else:
                    print(f"下载中文版: {zh_pdf['title'][:50]}...")
                    if not self.download_file(zh_pdf["url"], zh_filepath):
                        pair_success = False

            if pair_success:
                successful_downloads += 1

            # 添加延迟
            if i < len(paired_pdfs):
                time.sleep(self.request_delay)

        print(
            f"\n下载完成! 成功下载 {successful_downloads}/{len(paired_pdfs)} 对PDF文件"
        )
        return successful_downloads

    def save_results(
        self,
        en_docs,
        en_pdfs,
        zh_docs,
        zh_pdfs,
        paired_pdfs,
        filename="table_results.json",
    ):
        """保存结果"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "english_documents": len(en_docs),
            "english_pdfs": len(en_pdfs),
            "chinese_documents": len(zh_docs),
            "chinese_pdfs": len(zh_pdfs),
            "total_pairs": len(paired_pdfs),
            "documents": {"english": en_docs, "chinese": zh_docs},
            "pdfs": {"english": en_pdfs, "chinese": zh_pdfs},
            "pairs": paired_pdfs,
        }

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"详细结果已保存到: {filename}")


def main():
    """主函数"""
    print("香港交易所PDF爬虫 - 表格解析版本")
    print("专门解析搜索结果表格，获取所有文档")
    print("=" * 50)

    scraper = HKEXTableScraper()

    # 从config.py读取参数或使用默认值
    try:
        import config

        search_params = config.SEARCH_PARAMS
        from_date = search_params.get("fromDate", "01/01/2024")
        to_date = search_params.get("toDate", "31/12/2024")
        stock_code = search_params.get("stockId", "01760")
        title_keyword = search_params.get("title", "")
    except:
        from_date = "01/01/2024"
        to_date = "31/12/2024"
        stock_code = "01760"
        title_keyword = ""

    print(f"使用搜索参数:")
    print(f"  股票代码: {stock_code}")
    print(f"  日期范围: {from_date} 到 {to_date}")
    print(f"  关键词: '{title_keyword}' (空表示所有)")

    # 搜索文档
    en_docs, en_pdfs, zh_docs, zh_pdfs = scraper.search_paired_documents(
        from_date=from_date,
        to_date=to_date,
        title_keyword=title_keyword,
        stock_code=stock_code,
        max_pages=5,
    )

    print(f"\n总搜索结果:")
    print(f"  英文文档: {len(en_docs)} 个")
    print(f"  英文PDF: {len(en_pdfs)} 个")
    print(f"  中文文档: {len(zh_docs)} 个")
    print(f"  中文PDF: {len(zh_pdfs)} 个")

    if not en_pdfs and not zh_pdfs:
        print("没有找到任何PDF文件")
        return

    # 配对PDF
    paired_pdfs = scraper.pair_pdfs_by_position(en_pdfs, zh_pdfs)

    # 保存详细结果
    scraper.save_results(en_docs, en_pdfs, zh_docs, zh_pdfs, paired_pdfs)

    if not paired_pdfs:
        print("没有找到配对的PDF文件")
        return

    # 询问是否下载
    download = input("\n是否开始下载? (y/n): ").lower().strip()

    if download == "y":
        # 下载PDF
        scraper.download_pdf_pairs(paired_pdfs)
        print("\n🎉 下载完成!")
        print(f"英文PDF保存在: {scraper.en_dir}")
        print(f"中文PDF保存在: {scraper.tc_dir}")
    else:
        print("跳过下载")


if __name__ == "__main__":
    main()
