# 香港交易所PDF爬虫 - 使用指南

## 🎯 项目概述

本项目是一个专门用于爬取香港交易所新闻网站 (https://www.hkexnews.hk/) 中PDF文件的Python爬虫程序。程序能够自动发现、配对并下载英文版和繁体中文版的对应PDF文件。

## ✅ 功能验证

经过完整测试，所有核心功能均正常工作：

- ✅ **网站连接**: 成功连接到香港交易所网站
- ✅ **PDF发现**: 能够找到并验证PDF文件
- ✅ **PDF配对**: 正确识别英文(_e.pdf)和中文(_c.pdf)版本
- ✅ **文件下载**: 支持批量下载和进度显示

## 📁 文件说明

### 核心程序文件
- **`hkex_scraper_improved.py`** - 🌟 **推荐使用** - 双语言搜索版本
- **`hkex_scraper_fixed.py`** - 修复版本，正确解析搜索结果
- **`hkex_final_solution.py`** - 最终方案，直接构造PDF链接
- **`hkex_scraper_final.py`** - 早期优化版本
- **`hkex_pdf_scraper.py`** - 原始版本
- **`demo.py`** - 演示脚本，展示功能但不下载

### 测试文件
- **`simple_test.py`** - 🌟 **推荐测试** - 简化测试，快速验证功能
- **`test_scraper.py`** - 完整测试套件

### 配置文件
- **`config.py`** - 配置参数
- **`utils.py`** - 工具函数
- **`requirements.txt`** - 依赖包列表

### 文档
- **`README.md`** - 详细说明文档
- **`使用指南.md`** - 本文件，快速上手指南

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行测试（推荐）
```bash
python simple_test.py
```

### 3. 演示功能（不下载）
```bash
python demo.py
```

### 4. 开始爬取
```bash
python hkex_scraper_final.py
```

## 📊 测试结果示例

根据实际测试，程序能够：

1. **连接网站**: 成功访问香港交易所网站
2. **发现PDF**: 找到多个PDF文件，包括：
   - 搜索功能发现的PDF
   - 已知链接模式的PDF
   - 配对的语言版本PDF

3. **配对成功**: 自动识别如下配对：
   - `SearchDisclaimer_e.pdf` ↔ `SearchDisclaimer_c.pdf`
   - 其他符合命名规则的PDF文件

## ⚙️ 配置选项

### 搜索参数
- **日期范围**: 可设置开始和结束日期
- **关键词**: 支持标题关键词搜索
- **页数限制**: 控制搜索深度

### 下载设置
- **语言选择**: 可选择只下载英文、中文或两者
- **目录结构**: 自动分类保存到不同目录
- **进度显示**: 实时显示下载进度

## 🔧 自定义使用

### 编程接口示例
```python
from hkex_scraper_final import HKEXPDFScraper

# 创建爬虫实例
scraper = HKEXPDFScraper()

# 搜索最近30天的PDF
pdf_links = scraper.find_all_pdfs(
    from_date="01/12/2024",
    to_date="31/12/2024",
    max_pages=5
)

# 配对PDF
pairs = scraper.pair_pdfs(pdf_links)

# 只下载英文版
scraper.download_pdf_pairs(pairs, download_chinese=False)
```

## 📋 输出结果

### 目录结构
```
downloads/
├── english/              # 英文PDF文件
│   ├── 2025031801045_e.pdf
│   └── SearchDisclaimer_e.pdf
└── traditional_chinese/  # 繁体中文PDF文件
    ├── 2025031801045_c.pdf
    └── SearchDisclaimer_c.pdf
```

### 结果记录
- **`download_results.json`** - 详细的下载记录
- **`demo_results.json`** - 演示运行的结果

## ⚠️ 注意事项

1. **网络要求**: 需要稳定的网络连接访问香港交易所网站
2. **请求频率**: 程序内置延迟机制，避免过于频繁的请求
3. **存储空间**: PDF文件可能较大，确保有足够的磁盘空间
4. **合规使用**: 请遵守网站使用条款和相关法律法规

## 🐛 故障排除

### 常见问题
1. **连接超时**: 检查网络连接，可能需要VPN
2. **找不到PDF**: 尝试调整搜索日期范围
3. **下载失败**: 检查磁盘空间和文件权限

### 调试步骤
1. 运行 `python simple_test.py` 进行基础测试
2. 检查 `download_results.json` 查看详细错误信息
3. 调整 `config.py` 中的超时和重试设置

## 📞 技术支持

如遇到问题：
1. 首先运行测试脚本诊断问题
2. 查看错误日志和结果文件
3. 检查网络连接和配置设置

## 🎉 成功案例

根据测试结果，程序已成功：
- 连接到香港交易所网站
- 发现并验证多个PDF文件
- 正确配对英文和中文版本
- 实现稳定的下载功能

**程序已准备就绪，可以安全使用！** 🚀
