"""
香港交易所PDF爬虫 - 最终版本
基于测试结果优化的完整爬虫程序
"""

import os
import re
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from datetime import datetime, timedelta
from tqdm import tqdm
import json


class HKEXPDFScraper:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
        }
        self.session.headers.update(self.headers)

        # 配置
        self.base_url = "https://www1.hkexnews.hk"
        self.search_url_en = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
        self.search_url_zh = "https://www1.hkexnews.hk/search/titlesearch.xhtml?lang=zh"
        self.download_dir = "downloads"
        self.en_dir = os.path.join(self.download_dir, "english")
        self.tc_dir = os.path.join(self.download_dir, "traditional_chinese")
        self.request_delay = 1
        self.timeout = 30

        # 创建下载目录
        os.makedirs(self.en_dir, exist_ok=True)
        os.makedirs(self.tc_dir, exist_ok=True)

    def extract_pdf_id(self, pdf_url):
        """提取PDF ID用于配对"""
        filename = os.path.basename(pdf_url)

        # 模式1: 数字ID + 语言后缀 (如: 2025031801045_e.pdf)
        match = re.match(r"(\d+)_[ec]\.pdf", filename)
        if match:
            return match.group(1)

        # 模式2: 文件名 + 语言后缀 (如: SearchDisclaimer_e.pdf)
        match = re.match(r"(.+)_[ec]\.pdf", filename)
        if match:
            return match.group(1)

        # 模式3: 纯数字文件名 (如: 2025031801045.pdf)
        match = re.match(r"(\d+)\.pdf", filename)
        if match:
            return match.group(1)

        return None

    def is_english_pdf(self, pdf_url):
        """判断是否为英文PDF"""
        return pdf_url.endswith("_e.pdf")

    def is_chinese_pdf(self, pdf_url):
        """判断是否为中文PDF"""
        return pdf_url.endswith("_c.pdf")

    def search_pdfs_by_search_function(
        self,
        from_date="01/01/2024",
        to_date="31/12/2024",
        title_keyword="",
        max_pages=5,
    ):
        """使用搜索功能查找PDF"""
        print("使用搜索功能查找PDF...")

        search_params = {
            "lang": "EN",
            "category": "0",
            "market": "SEHK",
            "stockId": "-1",
            "documentType": "-1",
            "fromDate": from_date,
            "toDate": to_date,
            "title": title_keyword,
            "searchType": "1",
        }

        pdf_links = []

        for page in range(1, max_pages + 1):
            search_params["currentPageNo"] = str(page)

            try:
                response = self.session.get(
                    self.search_url, params=search_params, timeout=self.timeout
                )
                response.raise_for_status()

                soup = BeautifulSoup(response.content, "html.parser")

                # 查找PDF链接
                page_pdfs = []
                for link in soup.find_all("a", href=True):
                    href = link["href"]
                    if ".pdf" in href:
                        full_url = (
                            href
                            if href.startswith("http")
                            else urljoin(self.base_url, href)
                        )
                        page_pdfs.append(
                            {
                                "url": full_url,
                                "filename": os.path.basename(href),
                                "title": link.get_text(strip=True),
                            }
                        )

                if not page_pdfs:
                    print(f"第 {page} 页没有找到PDF，停止搜索")
                    break

                pdf_links.extend(page_pdfs)
                print(f"第 {page} 页找到 {len(page_pdfs)} 个PDF")

                time.sleep(self.request_delay)

            except Exception as e:
                print(f"搜索第 {page} 页时出错: {str(e)}")
                break

        return pdf_links

    def search_pdfs_by_known_patterns(self):
        """使用已知的PDF链接模式"""
        print("使用已知PDF链接模式...")

        known_pdfs = [
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2025/0318/2025031801045.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0926/2024092600815.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0916/2024091600585.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1118/2024111800749.pdf",
            "https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0926/2024092600403.pdf",
        ]

        pdf_links = []

        for pdf_url in known_pdfs:
            try:
                response = self.session.head(pdf_url, timeout=self.timeout)
                if response.status_code == 200:
                    filename = os.path.basename(pdf_url)
                    pdf_links.append(
                        {
                            "url": pdf_url,
                            "filename": filename,
                            "title": f"已知PDF - {filename}",
                        }
                    )
                    print(f"验证PDF: {filename} ✓")

                    # 尝试找到对应的语言版本
                    if not ("_e.pdf" in pdf_url or "_c.pdf" in pdf_url):
                        base_url = pdf_url.replace(".pdf", "")
                        for suffix in ["_e.pdf", "_c.pdf"]:
                            test_url = f"{base_url}{suffix}"
                            try:
                                test_response = self.session.head(test_url, timeout=5)
                                if test_response.status_code == 200:
                                    test_filename = os.path.basename(test_url)
                                    pdf_links.append(
                                        {
                                            "url": test_url,
                                            "filename": test_filename,
                                            "title": f"配对PDF - {test_filename}",
                                        }
                                    )
                                    print(f"找到配对PDF: {test_filename} ✓")
                            except:
                                pass

            except Exception as e:
                print(f"验证PDF失败: {os.path.basename(pdf_url)} - {str(e)}")

        return pdf_links

    def find_all_pdfs(
        self,
        from_date="01/01/2024",
        to_date="31/12/2024",
        title_keyword="",
        max_pages=3,
    ):
        """综合搜索PDF文件"""
        print("开始搜索PDF文件...")

        all_pdfs = []

        # 方法1: 搜索功能
        search_pdfs = self.search_pdfs_by_search_function(
            from_date, to_date, title_keyword, max_pages
        )
        all_pdfs.extend(search_pdfs)

        # 方法2: 已知模式
        known_pdfs = self.search_pdfs_by_known_patterns()
        all_pdfs.extend(known_pdfs)

        # 去重
        unique_pdfs = {}
        for pdf in all_pdfs:
            unique_pdfs[pdf["url"]] = pdf

        final_pdfs = list(unique_pdfs.values())
        print(f"总共找到 {len(final_pdfs)} 个唯一PDF文件")

        return final_pdfs

    def pair_pdfs(self, pdf_links):
        """配对英文和中文PDF"""
        print("配对英文和中文PDF...")

        english_pdfs = {}
        chinese_pdfs = {}

        for pdf in pdf_links:
            pdf_id = self.extract_pdf_id(pdf["url"])
            if not pdf_id:
                continue

            if self.is_english_pdf(pdf["url"]):
                english_pdfs[pdf_id] = pdf
            elif self.is_chinese_pdf(pdf["url"]):
                chinese_pdfs[pdf_id] = pdf

        # 找到配对的PDF
        paired_pdfs = []
        for pdf_id in english_pdfs:
            if pdf_id in chinese_pdfs:
                paired_pdfs.append(
                    {
                        "id": pdf_id,
                        "english": english_pdfs[pdf_id],
                        "chinese": chinese_pdfs[pdf_id],
                    }
                )

        print(f"英文PDF: {len(english_pdfs)} 个")
        print(f"中文PDF: {len(chinese_pdfs)} 个")
        print(f"配对PDF: {len(paired_pdfs)} 对")

        return paired_pdfs

    def download_file(self, url, filepath):
        """下载文件"""
        try:
            response = self.session.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()

            total_size = int(response.headers.get("content-length", 0))

            with open(filepath, "wb") as f:
                if total_size > 0:
                    with tqdm(
                        total=total_size,
                        unit="B",
                        unit_scale=True,
                        desc=os.path.basename(filepath),
                    ) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

            print(f"下载完成: {filepath}")
            return True

        except Exception as e:
            print(f"下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False

    def download_pdf_pairs(
        self, paired_pdfs, download_english=True, download_chinese=True
    ):
        """下载配对的PDF文件"""
        print(f"开始下载 {len(paired_pdfs)} 对PDF文件...")

        successful_downloads = 0

        for i, pair in enumerate(paired_pdfs, 1):
            print(f"\n处理第 {i}/{len(paired_pdfs)} 对PDF (ID: {pair['id']})")

            pair_success = True

            # 下载英文版
            if download_english:
                en_filename = pair["english"]["filename"]
                en_filepath = os.path.join(self.en_dir, en_filename)

                if os.path.exists(en_filepath):
                    print(f"英文版已存在: {en_filename}")
                else:
                    print(f"下载英文版: {pair['english']['title']}")
                    if not self.download_file(pair["english"]["url"], en_filepath):
                        pair_success = False

            # 下载中文版
            if download_chinese:
                cn_filename = pair["chinese"]["filename"]
                cn_filepath = os.path.join(self.tc_dir, cn_filename)

                if os.path.exists(cn_filepath):
                    print(f"中文版已存在: {cn_filename}")
                else:
                    print(f"下载中文版: {pair['chinese']['title']}")
                    if not self.download_file(pair["chinese"]["url"], cn_filepath):
                        pair_success = False

            if pair_success:
                successful_downloads += 1

            # 添加延迟
            if i < len(paired_pdfs):
                time.sleep(self.request_delay)

        print(
            f"\n下载完成! 成功下载 {successful_downloads}/{len(paired_pdfs)} 对PDF文件"
        )
        return successful_downloads

    def save_results(self, paired_pdfs, filename="download_results.json"):
        """保存下载结果"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "total_pairs": len(paired_pdfs),
            "pairs": paired_pdfs,
        }

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"结果已保存到: {filename}")


def main():
    """主函数"""
    print("香港交易所PDF爬虫 - 最终版本")
    print("=" * 50)

    scraper = HKEXPDFScraper()

    # 搜索PDF文件
    pdf_links = scraper.find_all_pdfs(
        from_date="01/01/2024", to_date="31/12/2024", title_keyword="", max_pages=3
    )

    if not pdf_links:
        print("没有找到PDF文件")
        return

    # 配对PDF
    paired_pdfs = scraper.pair_pdfs(pdf_links)

    if not paired_pdfs:
        print("没有找到配对的PDF文件")
        return

    # 保存结果
    scraper.save_results(paired_pdfs)

    # 询问是否下载
    print(f"\n找到 {len(paired_pdfs)} 对配对的PDF文件")
    for pair in paired_pdfs:
        print(
            f"  {pair['id']}: {pair['english']['filename']} <-> {pair['chinese']['filename']}"
        )

    download = input("\n是否开始下载? (y/n): ").lower().strip()

    if download == "y":
        # 下载PDF
        scraper.download_pdf_pairs(paired_pdfs)
        print("\n🎉 下载完成!")
    else:
        print("跳过下载")


if __name__ == "__main__":
    main()
