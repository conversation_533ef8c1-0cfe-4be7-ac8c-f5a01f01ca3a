

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<!-- For EPS search -->



<!-- For EPS search end -->



<html class="news-hkex">

	<HEAD>

		<title>Hong Kong Exchanges and Clearing Limited</title>

    		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">    

	        <meta name="viewport" content="width=device-width, initial-scale=1">

                <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    		<link href="/ncms/css/main.css" rel="stylesheet" />

    		<script type="text/javascript" src="/ncms/script/hkex_app.js"></script>

    		<script type="text/javascript" src="/ncms/script/hkex_settings.js"></script>

    		<script type="text/javascript" src="/ncms/script/hkex_widget.js"></script>

    		<script type="text/javascript" src="/ncms/script/vendor.js"></script>

		<LINK href="../css/hkex_css.css" type="text/css" rel="stylesheet">

	<script type="text/javascript">

	

	// JS variables, please set before hkex_pagebanner.js

	var pageDefaultTitle = "Search by listed corporation - Current Securities"; 

	// TO override page title 

        var pageDefaultBanner = "/ncms/media/HKEXnews/top_banner_bg.png"; // TO override page banner for desktop

        var pageDefaultTabletBanner = "/ncms/media/HKEXnews/top_banner_bg_tablet.png"; // TO override page banner for tablet

        // TO override breadcrumb

	

       	var overrideBreadcrumb = [{

            title: "Disclosure Of Interests (Notices Filed Through DION System Since 3 July 2017)",

            url: "https://www2.hkexnews.hk/Shareholding-Disclosures/Disclosure-of-Interests/Search-facilities---DI-from-3-July-2017?sc_lang=en"

        },

	{            

            title: "Disclosure Of Interests",

            url: "https://www2.hkexnews.hk/Shareholding-Disclosures/Disclosure-of-Interests?sc_lang=en"

        },

	{            

            title: "Shareholding Disclosures",

            url: "https://www2.hkexnews.hk/Shareholding-Disclosures?sc_lang=en"

        }];

		

        var overridePageTools = {};

        overridePageTools.showlastupdate = 0; // 1 for ture, 0 for false

        overridePageTools.lastupdate = "This is last updated date";

        overridePageTools.showprint = 0; // 1 for ture, 0 for false

        overridePageTools.printfriend = "Print FRIENDLY";        

	</script>

        <!-- Clickjacking Fix-->

    <style id="antiClickjack">

        body {

            display: none !important;

        }

    </style>   

    <script type="text/javascript" language="javascript" src="../js/hkex_clickjack.js"></script>



			<!-- PCCW Revamped 20040313 -->

			

		<!-- -->



    



	</HEAD>

<body>

    <a name="TOP"></a>

			

    <!-- CMS Control Header START -->

    <script type="text/javascript" src="/ncms/script/hkex_head.js"></script>

    <!-- CMS Control Header END -->



    <!-- CMS Control PageBanner START -->

    <script type="text/javascript" src="/ncms/script/hkex_pagebanner.js"></script>

    <!-- CMS Control PageBanner END -->

				



    <div id="skip-to-content"></div>



    <div class="content-container content-container-reset">

        <main class="di-page-content">

            <div class="container section-panel">

		<!-- PCCW Revamped 20040313 -->

		

		

		<table height="100%" cellSpacing="3" cellPadding="5" width="100%" bgColor="#ffffff" border="0">

			<tr>

				<td vAlign="top">

					

					<!-- -->

					<form method="post" action="NSSrchCorpQW.aspx?src=MAIN&amp;lang=EN&amp;in=1&amp;sc=01760&amp;" id="NSSrchCorp">

<div class="aspNetHidden">

<input type="hidden" name="__EVENTTARGET" id="__EVENTTARGET" value="" />

<input type="hidden" name="__EVENTARGUMENT" id="__EVENTARGUMENT" value="" />

<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="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" />

</div>



<script type="text/javascript">

//<![CDATA[

var theForm = document.forms['NSSrchCorp'];

if (!theForm) {

    theForm = document.NSSrchCorp;

}

function __doPostBack(eventTarget, eventArgument) {

    if (!theForm.onsubmit || (theForm.onsubmit() != false)) {

        theForm.__EVENTTARGET.value = eventTarget;

        theForm.__EVENTARGUMENT.value = eventArgument;

        theForm.submit();

    }

}

//]]>

</script>





<div class="aspNetHidden">



	<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="********" />

	<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="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" />

</div>

						<TABLE id="Table1" cellSpacing="0" cellPadding="0" width="100%" border="0">

							<TR>

								<TD class="title" colSpan="3"><span id="lblTitle"><IMG src='images/titleEn.gif'></span></TD>

							</TR>

							<TR>

								<TD><IMG id="IMG1" alt="" src="images/titLine.jpg" align="left"></TD>

							</TR>

							<TR>

								<TD>

									<TABLE id="Table3" cellSpacing="0" cellPadding="0" width="100%" border="0">

										<TR>

											<TD class="section" width="50%"><font face="Arial, Helvetica, sans-serif" color="#CC0000"> <b><span id="lblSearch">Search by listed corporation - Current Securities</span></TD>

											<TD class="section" align="right" width="50%">&nbsp;<A onclick="javascript:window.open('notes/NSSrchCorp.htm', '')" href="javascript:void(0);" ><IMG alt="" src="images\explannoteEN.gif" border=0 ></A></TD>

										</TR>

									</TABLE>

								</TD>

							</TR>

							<TR>

								<TD vAlign="top" width="770">

									<TABLE id="Table2" cellSpacing="0" cellPadding="0" width="100%" border="0">

										<TR>

											<TD colSpan="4">&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" colSpan="4" height="2"><span id="lblPlease">Please enter the stock code or the name of the listed corporation to search for one of the following types of reports:</span></TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" colSpan="3">

												<ul>

													<li>

														<span id="lblNote4">Complete list of substantial shareholders as of a particular date</span>

													<li>

														<span id="lblNote1">Consolidated list of substantial shareholders as of a particular date</span>

													<li>

														<span id="lblNote2">List of DI notices filed by substantial shareholders or directors who are also substantial shareholders with share interests not less than 5%</span>

													<li>

														<span id="lblNote5">Complete list of directors as of a particular date</span>

													<li>

														<span id="lblNote3">List of DI notices filed by directors</span>

													<li>

														<span id="lblNote6">List of all DI notices</span></li>

												</ul>

											</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top" colSpan="2"><span id="lblErrCriteria" style="color:Red;"></span></TD>

											<TD>&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top" colSpan="2">&nbsp;</TD>

											<TD>&nbsp;</TD>

										</TR>

                                        <TR>

                                            <TD width="5">&nbsp;</TD>

                                            <TD colspan="3">

                                                

                                                <a id="lnkSearchListedCorp" class="SrchCorp" href="javascript:__doPostBack(&#39;lnkSearchListedCorp&#39;,&#39;&#39;)">Search by listed corporation - Delisted Securities</a>

							                </TD>

                                        </TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top" colSpan="2">&nbsp;</TD>

											<TD>&nbsp;</TD>

										</TR>

                                        <TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top" width="100"><span id="lblStockCode">Stock code:</span></TD>

											<TD><input name="txtStockCode" type="text" value="01760" maxlength="5" size="5" id="txtStockCode" style="font-size: 9pt" /></TD>

											<TD>&nbsp;</TD>

										</TR>

										<tr>

                        								<td class="txt" colspan="3">&nbsp;</td>

                    								</tr>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top"><span id="lblLCorp">*Name of listed corporation:</span></TD>

											<TD class="txt"><input name="txtCorpName" type="text" maxlength="150" size="50" id="txtCorpName" style="font-size: 9pt" /><br>

												<A href="NSSelectCorp.aspx?src=MAIN&lang=EN&g_lang=en" >

													Choose from a list of corporations listed on the Exchange

												</A>

												<br>

												<br>

											</TD>

											<TD>&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" vAlign="top"><span id="lblDate">**Date:</span></TD>

											<TD class="txt">

												<TABLE id="Table4" cellSpacing="0" cellPadding="0" width="90%" border="0">

													<TR>

														<TD class="txt" width="200">

                                                            <select name="ddlStartDateDD" id="ddlStartDateDD" style="width:55px;">

	<option value="01">01</option>

	<option value="02">02</option>

	<option value="03">03</option>

	<option value="04">04</option>

	<option value="05">05</option>

	<option value="06">06</option>

	<option value="07">07</option>

	<option value="08">08</option>

	<option value="09">09</option>

	<option value="10">10</option>

	<option value="11">11</option>

	<option value="12">12</option>

	<option value="13">13</option>

	<option value="14">14</option>

	<option value="15">15</option>

	<option value="16">16</option>

	<option value="17">17</option>

	<option value="18">18</option>

	<option value="19">19</option>

	<option value="20">20</option>

	<option value="21">21</option>

	<option value="22">22</option>

	<option value="23">23</option>

	<option value="24">24</option>

	<option value="25">25</option>

	<option selected="selected" value="26">26</option>

	<option value="27">27</option>

	<option value="28">28</option>

	<option value="29">29</option>

	<option value="30">30</option>

	<option value="31">31</option>



</select>&nbsp;

															<select name="ddlStartDateMM" id="ddlStartDateMM" style="width:55px;">

	<option value="01">01</option>

	<option value="02">02</option>

	<option value="03">03</option>

	<option value="04">04</option>

	<option selected="selected" value="05">05</option>

	<option value="06">06</option>

	<option value="07">07</option>

	<option value="08">08</option>

	<option value="09">09</option>

	<option value="10">10</option>

	<option value="11">11</option>

	<option value="12">12</option>



</select>&nbsp;

															<select name="ddlStartDateYYYY" id="ddlStartDateYYYY" style="width:70px;">

	<option value="&lt; 2003">&lt; 2003</option>

	<option value="2003">2003</option>

	<option value="2004">2004</option>

	<option value="2005">2005</option>

	<option value="2006">2006</option>

	<option value="2007">2007</option>

	<option value="2008">2008</option>

	<option value="2009">2009</option>

	<option value="2010">2010</option>

	<option value="2011">2011</option>

	<option value="2012">2012</option>

	<option value="2013">2013</option>

	<option value="2014">2014</option>

	<option value="2015">2015</option>

	<option value="2016">2016</option>

	<option value="2017">2017</option>

	<option value="2018">2018</option>

	<option value="2019">2019</option>

	<option value="2020">2020</option>

	<option value="2021">2021</option>

	<option value="2022">2022</option>

	<option value="2023">2023</option>

	<option selected="selected" value="2024">2024</option>

	<option value="2025">2025</option>



</select>

														</TD>

														<TD class="txt" align="middle" width="40"><span id="lblTo">to </span></TD>

														<TD class="txt" width="400">

                                                            <select name="ddlEndDateDD" id="ddlEndDateDD" style="width:55px;">

	<option value="01">01</option>

	<option value="02">02</option>

	<option value="03">03</option>

	<option value="04">04</option>

	<option value="05">05</option>

	<option value="06">06</option>

	<option value="07">07</option>

	<option value="08">08</option>

	<option value="09">09</option>

	<option value="10">10</option>

	<option value="11">11</option>

	<option value="12">12</option>

	<option value="13">13</option>

	<option value="14">14</option>

	<option value="15">15</option>

	<option value="16">16</option>

	<option value="17">17</option>

	<option value="18">18</option>

	<option value="19">19</option>

	<option value="20">20</option>

	<option value="21">21</option>

	<option value="22">22</option>

	<option value="23">23</option>

	<option value="24">24</option>

	<option value="25">25</option>

	<option selected="selected" value="26">26</option>

	<option value="27">27</option>

	<option value="28">28</option>

	<option value="29">29</option>

	<option value="30">30</option>

	<option value="31">31</option>



</select>&nbsp;

															<select name="ddlEndDateMM" id="ddlEndDateMM" style="width:55px;">

	<option value="01">01</option>

	<option value="02">02</option>

	<option value="03">03</option>

	<option value="04">04</option>

	<option selected="selected" value="05">05</option>

	<option value="06">06</option>

	<option value="07">07</option>

	<option value="08">08</option>

	<option value="09">09</option>

	<option value="10">10</option>

	<option value="11">11</option>

	<option value="12">12</option>



</select>&nbsp;

															<select name="ddlEndDateYYYY" id="ddlEndDateYYYY" style="width:70px;">

	<option value="2003">2003</option>

	<option value="2004">2004</option>

	<option value="2005">2005</option>

	<option value="2006">2006</option>

	<option value="2007">2007</option>

	<option value="2008">2008</option>

	<option value="2009">2009</option>

	<option value="2010">2010</option>

	<option value="2011">2011</option>

	<option value="2012">2012</option>

	<option value="2013">2013</option>

	<option value="2014">2014</option>

	<option value="2015">2015</option>

	<option value="2016">2016</option>

	<option value="2017">2017</option>

	<option value="2018">2018</option>

	<option value="2019">2019</option>

	<option value="2020">2020</option>

	<option value="2021">2021</option>

	<option value="2022">2022</option>

	<option value="2023">2023</option>

	<option value="2024">2024</option>

	<option selected="selected" value="2025">2025</option>



</select>

														</TD>

													<TR>

													<TR>

														<TD class="txt" colSpan="2"><span id="lblFromFormat"> </span></TD>

														<TD class="txt"><span id="lblToFormat"> </span></TD>

													</TR>

													<TR>

														<TD class="txt" colSpan="2"><span id="lblStartDateError" style="color:Red;"></span></TD>

														<TD class="txt"><span id="lblEndDateError" style="color:Red;"></span></TD>

													</TR>

												</TABLE>

											</TD>

											<TD>&nbsp;

											</TD>

										</TR>

										<TR>

											<TD colSpan="5">&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" colSpan="4"><span id="lblRem1">* Partial match of the name of listed corporation requires at least 3 characters to be entered.</span></TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD class="txt" colSpan="4"><span id="lblRem2">** Enter a period or date depending on the type of report required.</span></TD>

										</TR>

										<TR>

											<TD colSpan="4">&nbsp;</TD>

										</TR>

                                            

										<TR>

											<TD colSpan="4">&nbsp;</TD>

										</TR>

										<TR>

											<TD width="5">&nbsp;</TD>

											<TD colSpan="4">

                                                <input type="submit" name="cmdSearch" value="Search" id="cmdSearch" />

                                                <input type="submit" name="cmdBack" value="Back" id="cmdBack" />

											</TD>

										</TR>

										<TR>

											<TD colSpan="5">&nbsp;</TD>

										</TR>

									</TABLE>

								</TD>

							</TR>

							<TR>

								<TD></TD>

							</TR>

						</TABLE>





  

					</form>

					<!-- PCCW Revamped 20040313 -->

					

				</td>

			</tr>

		</table>

		</div>

        </main>

    </div>



    <!-- CMS Control Footer START -->

    <script type="text/javascript" src="/ncms/script/hkex_foot.js"></script>

    <!-- CMS Control Footer END -->

    <!-- NEWS Main Scripts START -->

    <script type="text/javascript" src="/ncms/script/main.js"></script>

    <!-- NEWS Main Scripts END -->

		

		<!-- -->

	</body>

</HTML>

