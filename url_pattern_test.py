"""
URL模式测试 - 找出正确的PDF URL格式
"""

import requests


def test_url_patterns():
    """测试不同的URL模式"""
    print("测试不同的PDF URL模式...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # 基础信息
    base_url = "https://www1.hkexnews.hk/listedco/listconews/sehk"
    date = "2024/1129"
    date_str = "20241129"
    stock_code = "00741"
    
    # 测试不同的URL模式
    url_patterns = [
        # 我们知道这个存在
        f"{base_url}/2024/1129/2024112900741.pdf",
        
        # 带语言后缀的变体
        f"{base_url}/2024/1129/2024112900741_e.pdf",
        f"{base_url}/2024/1129/2024112900741_c.pdf",
        f"{base_url}/2024/1129/20241129{stock_code}_e.pdf",
        f"{base_url}/2024/1129/20241129{stock_code}_c.pdf",
        
        # 不同的股票代码格式
        f"{base_url}/2024/1129/20241129741.pdf",
        f"{base_url}/2024/1129/202411291760.pdf",
        f"{base_url}/2024/1129/2024112901760.pdf",
        
        # 不同的日期格式
        f"{base_url}/2024/1129/112900741.pdf",
        f"{base_url}/2024/1129/291100741.pdf",
        
        # 其他可能的格式
        f"{base_url}/2024/1129/LTN{date_str}{stock_code}.pdf",
        f"{base_url}/2024/1129/LTN{date_str}{stock_code}_e.pdf",
        f"{base_url}/2024/1129/LTN{date_str}{stock_code}_c.pdf",
    ]
    
    print(f"测试日期: 2024-11-29")
    print(f"股票代码: {stock_code}")
    print(f"总共测试 {len(url_patterns)} 个URL模式\n")
    
    existing_urls = []
    
    for i, url in enumerate(url_patterns, 1):
        try:
            response = session.head(url, timeout=10)
            status = "✓ 存在" if response.status_code == 200 else f"✗ {response.status_code}"
            print(f"{i:2d}. {status} - {url}")
            
            if response.status_code == 200:
                existing_urls.append(url)
                
        except Exception as e:
            print(f"{i:2d}. ✗ 错误 - {url} ({str(e)})")
    
    print(f"\n找到 {len(existing_urls)} 个存在的URL:")
    for url in existing_urls:
        print(f"  ✓ {url}")
    
    # 测试其他日期
    print(f"\n测试其他已知日期...")
    other_dates = [
        ("2024/1106", "20241106"),
        ("2024/1205", "20241205"),
    ]
    
    for date_path, date_str in other_dates:
        print(f"\n测试日期: {date_path}")
        
        test_urls = [
            f"{base_url}/{date_path}/{date_str}00741.pdf",
            f"{base_url}/{date_path}/{date_str}00741_e.pdf",
            f"{base_url}/{date_path}/{date_str}00741_c.pdf",
        ]
        
        for url in test_urls:
            try:
                response = session.head(url, timeout=10)
                status = "✓ 存在" if response.status_code == 200 else f"✗ {response.status_code}"
                print(f"  {status} - {url}")
                
                if response.status_code == 200:
                    existing_urls.append(url)
                    
            except Exception as e:
                print(f"  ✗ 错误 - {url}")
    
    print(f"\n总结:")
    print(f"总共找到 {len(existing_urls)} 个存在的PDF文件:")
    for url in existing_urls:
        print(f"  {url}")
    
    # 分析模式
    if existing_urls:
        print(f"\nURL模式分析:")
        for url in existing_urls:
            filename = url.split('/')[-1]
            print(f"  文件名: {filename}")
            
            if '_e.pdf' in filename:
                print(f"    -> 英文版PDF")
            elif '_c.pdf' in filename:
                print(f"    -> 中文版PDF")
            elif '.pdf' in filename and '_' not in filename:
                print(f"    -> 无语言标识PDF (可能是双语或默认)")


if __name__ == "__main__":
    test_url_patterns()
