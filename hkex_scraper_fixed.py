"""
香港交易所PDF爬虫 - 修复版本
正确解析搜索结果，获取真正的上市公司披露报告
"""

import os
import re
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from datetime import datetime
from tqdm import tqdm
import json


class HKEXPDFScraperFixed:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)
        
        # 配置
        self.base_url = "https://www1.hkexnews.hk"
        self.search_url_en = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
        self.search_url_zh = "https://www1.hkexnews.hk/search/titlesearch.xhtml?lang=zh"
        self.download_dir = "downloads"
        self.en_dir = os.path.join(self.download_dir, "english")
        self.tc_dir = os.path.join(self.download_dir, "traditional_chinese")
        self.request_delay = 2  # 增加延迟避免被限制
        self.timeout = 30
        
        # 创建下载目录
        os.makedirs(self.en_dir, exist_ok=True)
        os.makedirs(self.tc_dir, exist_ok=True)
    
    def get_search_params(self, from_date, to_date, title_keyword, stock_code):
        """获取搜索参数"""
        try:
            import config
            search_params = config.SEARCH_PARAMS.copy()
            search_params.update({
                'fromDate': from_date,
                'toDate': to_date,
                'title': title_keyword,
                'stockId': stock_code
            })
            return search_params
        except:
            # 默认参数
            return {
                'lang': 'EN',
                'category': '0',
                'market': 'SEHK',
                'stockId': stock_code,
                'documentType': '-1',
                'fromDate': from_date,
                'toDate': to_date,
                'title': title_keyword,
                'searchType': '1'
            }
    
    def extract_document_links_from_search(self, soup):
        """从搜索结果页面提取文档链接"""
        document_links = []
        
        # 方法1: 查找表格中的链接
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for cell in cells:
                    links = cell.find_all('a', href=True)
                    for link in links:
                        href = link['href']
                        text = link.get_text(strip=True)
                        
                        # 查找指向文档页面的链接
                        if ('listedco' in href and 'listconews' in href and 
                            ('.htm' in href or '.html' in href)):
                            full_url = href if href.startswith('http') else urljoin(self.base_url, href)
                            document_links.append({
                                'url': full_url,
                                'title': text,
                                'type': 'document_page'
                            })
        
        # 方法2: 查找所有可能的文档链接
        all_links = soup.find_all('a', href=True)
        for link in all_links:
            href = link['href']
            text = link.get_text(strip=True)
            
            # 更精确的文档链接模式
            if (('listedco' in href and 'listconews' in href) and 
                ('.htm' in href or '.html' in href) and
                len(text) > 10):  # 过滤掉太短的链接文本
                full_url = href if href.startswith('http') else urljoin(self.base_url, href)
                
                # 避免重复
                if not any(doc['url'] == full_url for doc in document_links):
                    document_links.append({
                        'url': full_url,
                        'title': text,
                        'type': 'document_page'
                    })
        
        return document_links
    
    def extract_pdfs_from_document_page(self, doc_url, doc_title):
        """从文档页面提取PDF链接"""
        try:
            response = self.session.get(doc_url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            pdf_links = []
            
            # 查找PDF链接
            for link in soup.find_all('a', href=True):
                href = link['href']
                link_text = link.get_text(strip=True)
                
                if '.pdf' in href.lower():
                    full_url = href if href.startswith('http') else urljoin(self.base_url, href)
                    
                    # 过滤掉免责声明等无关PDF
                    if ('disclaimer' not in href.lower() and 
                        'terms' not in href.lower() and
                        len(link_text) > 5):
                        
                        pdf_links.append({
                            'url': full_url,
                            'filename': os.path.basename(href),
                            'title': link_text if link_text else doc_title,
                            'source_doc': doc_title,
                            'source_url': doc_url
                        })
            
            return pdf_links
            
        except Exception as e:
            print(f"  提取PDF失败 {doc_url}: {str(e)}")
            return []
    
    def search_documents_by_language(self, search_url, lang_name, from_date, to_date, 
                                   title_keyword, stock_code, max_pages):
        """按语言搜索文档"""
        print(f"搜索{lang_name}版本文档...")
        
        search_params = self.get_search_params(from_date, to_date, title_keyword, stock_code)
        all_pdfs = []
        
        for page in range(1, max_pages + 1):
            search_params['currentPageNo'] = str(page)
            
            try:
                print(f"  正在搜索{lang_name}第 {page} 页...")
                response = self.session.get(search_url, params=search_params, timeout=self.timeout)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 提取文档链接
                document_links = self.extract_document_links_from_search(soup)
                
                if not document_links:
                    print(f"  {lang_name}第 {page} 页没有找到文档链接，停止搜索")
                    break
                
                print(f"  {lang_name}第 {page} 页找到 {len(document_links)} 个文档")
                
                # 从每个文档页面提取PDF
                page_pdfs = []
                for i, doc in enumerate(document_links):
                    print(f"    处理文档 {i+1}/{len(document_links)}: {doc['title'][:50]}...")
                    
                    pdfs = self.extract_pdfs_from_document_page(doc['url'], doc['title'])
                    page_pdfs.extend(pdfs)
                    
                    # 添加延迟避免过于频繁的请求
                    time.sleep(0.5)
                
                all_pdfs.extend(page_pdfs)
                print(f"  {lang_name}第 {page} 页提取到 {len(page_pdfs)} 个PDF")
                
                time.sleep(self.request_delay)
                
            except Exception as e:
                print(f"  搜索{lang_name}第 {page} 页时出错: {str(e)}")
                break
        
        print(f"{lang_name}版本总共找到 {len(all_pdfs)} 个PDF")
        return all_pdfs
    
    def search_paired_documents(self, from_date="01/01/2024", to_date="31/12/2024", 
                               title_keyword="", stock_code="00700", max_pages=5):
        """搜索配对的文档"""
        print("开始搜索配对的文档...")
        print(f"搜索参数: 股票代码={stock_code}, 日期={from_date}到{to_date}")
        
        # 搜索英文版本
        english_pdfs = self.search_documents_by_language(
            self.search_url_en, "英文", from_date, to_date, 
            title_keyword, stock_code, max_pages
        )
        
        # 搜索中文版本
        chinese_pdfs = self.search_documents_by_language(
            self.search_url_zh, "中文", from_date, to_date, 
            title_keyword, stock_code, max_pages
        )
        
        return english_pdfs, chinese_pdfs
    
    def pair_pdfs_by_filename(self, english_pdfs, chinese_pdfs):
        """根据文件名模式配对PDF"""
        print("根据文件名模式配对PDF...")
        
        paired_pdfs = []
        
        # 创建英文PDF的索引
        en_index = {}
        for pdf in english_pdfs:
            # 提取文件名的基础部分（去掉语言标识）
            filename = pdf['filename']
            base_name = re.sub(r'_[ec]\.pdf$', '', filename, flags=re.IGNORECASE)
            if base_name not in en_index:
                en_index[base_name] = []
            en_index[base_name].append(pdf)
        
        # 匹配中文PDF
        for zh_pdf in chinese_pdfs:
            filename = zh_pdf['filename']
            base_name = re.sub(r'_[ec]\.pdf$', '', filename, flags=re.IGNORECASE)
            
            if base_name in en_index:
                for en_pdf in en_index[base_name]:
                    paired_pdfs.append({
                        'id': base_name,
                        'english': en_pdf,
                        'chinese': zh_pdf
                    })
                    break  # 只配对第一个匹配的
        
        print(f"根据文件名配对成功: {len(paired_pdfs)} 对")
        return paired_pdfs
    
    def pair_pdfs_by_position(self, english_pdfs, chinese_pdfs):
        """按位置配对PDF"""
        print("按位置配对PDF...")
        
        paired_pdfs = []
        min_length = min(len(english_pdfs), len(chinese_pdfs))
        
        for i in range(min_length):
            paired_pdfs.append({
                'id': f"position_{i+1}",
                'english': english_pdfs[i],
                'chinese': chinese_pdfs[i],
                'position': i + 1
            })
        
        print(f"按位置配对成功: {len(paired_pdfs)} 对")
        return paired_pdfs
    
    def download_file(self, url, filepath):
        """下载文件"""
        try:
            response = self.session.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                if total_size > 0:
                    with tqdm(total=total_size, unit='B', unit_scale=True, 
                             desc=os.path.basename(filepath)) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            
            print(f"下载完成: {filepath}")
            return True
            
        except Exception as e:
            print(f"下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False
    
    def download_pdf_pairs(self, paired_pdfs, download_english=True, download_chinese=True):
        """下载配对的PDF文件"""
        print(f"开始下载 {len(paired_pdfs)} 对PDF文件...")
        
        successful_downloads = 0
        
        for i, pair in enumerate(paired_pdfs, 1):
            print(f"\n处理第 {i}/{len(paired_pdfs)} 对PDF (ID: {pair['id']})")
            
            pair_success = True
            
            # 下载英文版
            if download_english:
                en_pdf = pair['english']
                en_filename = f"en_{i:03d}_{en_pdf['filename']}"
                en_filepath = os.path.join(self.en_dir, en_filename)
                
                if os.path.exists(en_filepath):
                    print(f"英文版已存在: {en_filename}")
                else:
                    print(f"下载英文版: {en_pdf['title'][:50]}...")
                    if not self.download_file(en_pdf['url'], en_filepath):
                        pair_success = False
            
            # 下载中文版
            if download_chinese:
                zh_pdf = pair['chinese']
                zh_filename = f"zh_{i:03d}_{zh_pdf['filename']}"
                zh_filepath = os.path.join(self.tc_dir, zh_filename)
                
                if os.path.exists(zh_filepath):
                    print(f"中文版已存在: {zh_filename}")
                else:
                    print(f"下载中文版: {zh_pdf['title'][:50]}...")
                    if not self.download_file(zh_pdf['url'], zh_filepath):
                        pair_success = False
            
            if pair_success:
                successful_downloads += 1
            
            # 添加延迟
            if i < len(paired_pdfs):
                time.sleep(self.request_delay)
        
        print(f"\n下载完成! 成功下载 {successful_downloads}/{len(paired_pdfs)} 对PDF文件")
        return successful_downloads
    
    def save_results(self, paired_pdfs, filename="fixed_results.json"):
        """保存结果"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'total_pairs': len(paired_pdfs),
            'pairs': paired_pdfs
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到: {filename}")


def main():
    """主函数"""
    print("香港交易所PDF爬虫 - 修复版本")
    print("正确获取上市公司披露报告")
    print("=" * 50)
    
    scraper = HKEXPDFScraperFixed()
    
    # 从config.py读取参数或使用默认值
    try:
        import config
        search_params = config.SEARCH_PARAMS
        from_date = search_params.get('fromDate', '01/01/2024')
        to_date = search_params.get('toDate', '31/12/2024')
        stock_code = search_params.get('stockId', '00700')  # 腾讯控股
        title_keyword = search_params.get('title', '')
    except:
        from_date = "01/01/2024"
        to_date = "31/12/2024"
        stock_code = "00700"  # 腾讯控股
        title_keyword = ""
    
    print(f"使用搜索参数:")
    print(f"  股票代码: {stock_code}")
    print(f"  日期范围: {from_date} 到 {to_date}")
    print(f"  关键词: '{title_keyword}' (空表示所有)")
    
    # 搜索文档
    english_pdfs, chinese_pdfs = scraper.search_paired_documents(
        from_date=from_date,
        to_date=to_date,
        title_keyword=title_keyword,
        stock_code=stock_code,
        max_pages=3  # 限制页数避免过多请求
    )
    
    if not english_pdfs and not chinese_pdfs:
        print("没有找到任何PDF文件")
        return
    
    print(f"\n搜索结果:")
    print(f"  英文PDF: {len(english_pdfs)} 个")
    print(f"  中文PDF: {len(chinese_pdfs)} 个")
    
    # 尝试两种配对方式
    filename_pairs = scraper.pair_pdfs_by_filename(english_pdfs, chinese_pdfs)
    position_pairs = scraper.pair_pdfs_by_position(english_pdfs, chinese_pdfs)
    
    # 选择更好的配对结果
    if len(filename_pairs) > 0:
        paired_pdfs = filename_pairs
        print("使用文件名配对方式")
    else:
        paired_pdfs = position_pairs
        print("使用位置配对方式")
    
    if not paired_pdfs:
        print("没有找到配对的PDF文件")
        return
    
    # 显示配对结果
    print(f"\n找到 {len(paired_pdfs)} 对配对的PDF文件:")
    for i, pair in enumerate(paired_pdfs[:5], 1):
        print(f"  {i}. 英文: {pair['english']['title'][:40]}")
        print(f"     中文: {pair['chinese']['title'][:40]}")
    
    if len(paired_pdfs) > 5:
        print(f"  ... 还有 {len(paired_pdfs) - 5} 对")
    
    # 保存结果
    scraper.save_results(paired_pdfs)
    
    # 询问是否下载
    download = input("\n是否开始下载? (y/n): ").lower().strip()
    
    if download == 'y':
        # 下载PDF
        scraper.download_pdf_pairs(paired_pdfs)
        print("\n🎉 下载完成!")
        print(f"英文PDF保存在: {scraper.en_dir}")
        print(f"中文PDF保存在: {scraper.tc_dir}")
    else:
        print("跳过下载")


if __name__ == "__main__":
    main()
