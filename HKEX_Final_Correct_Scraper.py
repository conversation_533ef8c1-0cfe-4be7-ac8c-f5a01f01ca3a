"""
香港交易所PDF爬虫 - 最终正确版本
基于用户提供的截图信息，正确爬取01760 INTRON TECH的中英文对应报告

关键信息:
- 股票代码: 01760
- 公司名称: INTRON TECH (英文) / 英唐科技 (中文)
- 文档总数: 33份 (2024/01/01 - 2025/01/01)
- 中英文一一对应
"""

import os
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from datetime import datetime, timedelta
from tqdm import tqdm
import json
import time


class HKEXFinalCorrectScraper:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'https://www1.hkexnews.hk/',
        }
        self.session.headers.update(self.headers)
        
        # 配置
        self.base_url = "https://www1.hkexnews.hk"
        self.search_url_en = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
        self.search_url_zh = "https://www1.hkexnews.hk/search/titlesearch.xhtml?lang=zh"
        
        self.download_dir = "downloads_final_correct"
        self.paired_dir = os.path.join(self.download_dir, "paired_reports")
        self.en_dir = os.path.join(self.download_dir, "english")
        self.zh_dir = os.path.join(self.download_dir, "chinese")
        
        # 创建下载目录
        os.makedirs(self.paired_dir, exist_ok=True)
        os.makedirs(self.en_dir, exist_ok=True)
        os.makedirs(self.zh_dir, exist_ok=True)
        
        self.timeout = 30
        self.request_delay = 1
    
    def get_search_params(self, stock_code, from_date, to_date, language='en'):
        """获取搜索参数"""
        params = {
            'stockId': stock_code,  # 使用01760
            'fromDate': from_date,
            'toDate': to_date,
            'searchType': '1',
            'category': '0',
            'market': 'SEHK',
            'documentType': '-1',
            'title': '',
            'currentPageNo': '1'
        }
        
        if language == 'zh':
            params['lang'] = 'zh'
        
        return params
    
    def search_documents(self, stock_code, from_date, to_date, language='en', max_pages=10):
        """搜索文档"""
        print(f"搜索{language}版本文档...")
        
        search_url = self.search_url_zh if language == 'zh' else self.search_url_en
        all_documents = []
        
        for page in range(1, max_pages + 1):
            try:
                print(f"  正在搜索第 {page} 页...")
                
                params = self.get_search_params(stock_code, from_date, to_date, language)
                params['currentPageNo'] = str(page)
                
                response = self.session.get(search_url, params=params, timeout=self.timeout)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 查找结果表格
                page_documents = self.parse_search_results(soup, language)
                
                if not page_documents:
                    print(f"  第 {page} 页没有找到文档，停止搜索")
                    break
                
                all_documents.extend(page_documents)
                print(f"  第 {page} 页找到 {len(page_documents)} 个文档")
                
                time.sleep(self.request_delay)
                
            except Exception as e:
                print(f"  搜索第 {page} 页时出错: {str(e)}")
                break
        
        print(f"{language}版本总共找到: {len(all_documents)} 个文档")
        return all_documents
    
    def parse_search_results(self, soup, language):
        """解析搜索结果"""
        documents = []
        
        # 查找结果表格
        tables = soup.find_all('table')
        
        for table in tables:
            rows = table.find_all('tr')
            
            # 跳过表头，处理数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                
                if len(cells) >= 4:
                    try:
                        # 提取信息
                        release_time = cells[0].get_text(strip=True)
                        stock_code = cells[1].get_text(strip=True)
                        stock_name = cells[2].get_text(strip=True)
                        document_cell = cells[3]
                        
                        # 查找PDF链接
                        links = document_cell.find_all('a', href=True)
                        
                        for link in links:
                            href = link['href']
                            link_text = link.get_text(strip=True)
                            
                            # 过滤掉免责声明等无关链接
                            if ('disclaimer' not in href.lower() and 
                                'terms' not in href.lower() and
                                link_text.strip()):
                                
                                full_url = href if href.startswith('http') else urljoin(self.base_url, href)
                                
                                documents.append({
                                    'release_time': release_time,
                                    'stock_code': stock_code,
                                    'stock_name': stock_name,
                                    'title': link_text,
                                    'url': full_url,
                                    'language': language,
                                    'is_pdf': '.pdf' in href.lower()
                                })
                    
                    except Exception as e:
                        continue
        
        return documents
    
    def extract_pdf_from_document_page(self, doc_info):
        """从文档页面提取PDF链接"""
        if doc_info['is_pdf']:
            # 如果已经是PDF链接，直接返回
            return [doc_info]
        
        try:
            print(f"    访问文档页面: {doc_info['title'][:50]}...")
            response = self.session.get(doc_info['url'], timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            pdf_links = []
            
            # 查找PDF链接
            for link in soup.find_all('a', href=True):
                href = link['href']
                link_text = link.get_text(strip=True)
                
                if '.pdf' in href.lower():
                    full_url = href if href.startswith('http') else urljoin(self.base_url, href)
                    
                    # 过滤掉免责声明等
                    if ('disclaimer' not in href.lower() and 
                        'terms' not in href.lower()):
                        
                        pdf_info = doc_info.copy()
                        pdf_info.update({
                            'url': full_url,
                            'pdf_title': link_text if link_text else doc_info['title'],
                            'is_pdf': True
                        })
                        pdf_links.append(pdf_info)
            
            return pdf_links
            
        except Exception as e:
            print(f"    提取PDF失败: {str(e)}")
            return []
    
    def download_pdf(self, url, filepath):
        """下载PDF文件"""
        try:
            response = self.session.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                if total_size > 0:
                    with tqdm(total=total_size, unit='B', unit_scale=True, 
                             desc=os.path.basename(filepath)) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            
            print(f"✓ 下载成功: {filepath}")
            return True
            
        except Exception as e:
            print(f"✗ 下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False
    
    def pair_documents_by_date_and_title(self, en_docs, zh_docs):
        """按日期和标题配对文档"""
        print("\n配对中英文文档...")
        
        # 按日期分组
        en_by_date = {}
        zh_by_date = {}
        
        for doc in en_docs:
            date = doc['release_time']
            if date not in en_by_date:
                en_by_date[date] = []
            en_by_date[date].append(doc)
        
        for doc in zh_docs:
            date = doc['release_time']
            if date not in zh_by_date:
                zh_by_date[date] = []
            zh_by_date[date].append(doc)
        
        paired_docs = []
        
        # 配对同一日期的文档
        for date in en_by_date:
            if date in zh_by_date:
                en_docs_date = en_by_date[date]
                zh_docs_date = zh_by_date[date]
                
                # 简单配对：按顺序配对
                for i in range(min(len(en_docs_date), len(zh_docs_date))):
                    paired_docs.append({
                        'date': date,
                        'english': en_docs_date[i],
                        'chinese': zh_docs_date[i],
                        'pair_id': f"{date}_{i+1}"
                    })
        
        print(f"成功配对: {len(paired_docs)} 对文档")
        return paired_docs
    
    def download_paired_documents(self, paired_docs):
        """下载配对的文档"""
        print(f"\n开始下载 {len(paired_docs)} 对配对文档...")
        
        downloaded_pairs = 0
        
        for i, pair in enumerate(paired_docs, 1):
            print(f"\n处理第 {i}/{len(paired_docs)} 对文档 (日期: {pair['date']})")
            
            # 创建配对目录
            safe_date = pair['date'].replace('/', '-').replace(' ', '_')
            pair_dir = os.path.join(self.paired_dir, f"{safe_date}_{pair['pair_id']}")
            os.makedirs(pair_dir, exist_ok=True)
            
            pair_success = True
            
            # 处理英文版
            en_doc = pair['english']
            en_pdfs = self.extract_pdf_from_document_page(en_doc)
            
            for j, pdf in enumerate(en_pdfs):
                en_filename = f"EN_{safe_date}_{j+1}_{pdf['title'][:30].replace('/', '_')}.pdf"
                en_filepath = os.path.join(pair_dir, en_filename)
                
                if not os.path.exists(en_filepath):
                    if not self.download_pdf(pdf['url'], en_filepath):
                        pair_success = False
                else:
                    print(f"英文版已存在: {en_filename}")
            
            # 处理中文版
            zh_doc = pair['chinese']
            zh_pdfs = self.extract_pdf_from_document_page(zh_doc)
            
            for j, pdf in enumerate(zh_pdfs):
                zh_filename = f"ZH_{safe_date}_{j+1}_{pdf['title'][:30].replace('/', '_')}.pdf"
                zh_filepath = os.path.join(pair_dir, zh_filename)
                
                if not os.path.exists(zh_filepath):
                    if not self.download_pdf(pdf['url'], zh_filepath):
                        pair_success = False
                else:
                    print(f"中文版已存在: {zh_filename}")
            
            if pair_success:
                downloaded_pairs += 1
            
            time.sleep(self.request_delay)
        
        print(f"\n下载完成! 成功下载 {downloaded_pairs}/{len(paired_docs)} 对文档")
        return downloaded_pairs
    
    def scrape_intron_tech_reports(self, from_date="01/01/2024", to_date="01/01/2025"):
        """爬取INTRON TECH的配对报告"""
        print("=" * 60)
        print("🏢 爬取INTRON TECH (01760) 的配对报告")
        print("📄 确保中英文文档一一对应")
        print("=" * 60)
        
        stock_code = "01760"
        
        print(f"\n🎯 目标公司: INTRON TECH / 英唐科技")
        print(f"📊 股票代码: {stock_code}")
        print(f"📅 日期范围: {from_date} 到 {to_date}")
        
        # 搜索英文版文档
        en_docs = self.search_documents(stock_code, from_date, to_date, 'en')
        
        # 搜索中文版文档
        zh_docs = self.search_documents(stock_code, from_date, to_date, 'zh')
        
        if not en_docs and not zh_docs:
            print("❌ 没有找到任何文档")
            return
        
        print(f"\n📊 搜索结果:")
        print(f"  英文文档: {len(en_docs)} 个")
        print(f"  中文文档: {len(zh_docs)} 个")
        
        # 配对文档
        paired_docs = self.pair_documents_by_date_and_title(en_docs, zh_docs)
        
        if not paired_docs:
            print("❌ 没有找到配对的文档")
            return
        
        # 保存配对结果
        results = {
            'timestamp': datetime.now().isoformat(),
            'company': 'INTRON TECH / 英唐科技',
            'stock_code': stock_code,
            'date_range': f"{from_date} to {to_date}",
            'english_documents': len(en_docs),
            'chinese_documents': len(zh_docs),
            'paired_documents': len(paired_docs),
            'english_docs': en_docs,
            'chinese_docs': zh_docs,
            'paired_docs': paired_docs
        }
        
        results_file = f'intron_tech_paired_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细结果已保存到: {results_file}")
        
        # 询问是否下载
        download = input(f"\n是否下载 {len(paired_docs)} 对配对文档? (y/n): ").lower().strip()
        
        if download == 'y':
            downloaded_pairs = self.download_paired_documents(paired_docs)
            
            print(f"\n🎉 任务完成!")
            print(f"📁 配对文档保存在: {os.path.abspath(self.paired_dir)}")
            print(f"📊 成功下载: {downloaded_pairs} 对文档")
            print(f"✅ 解决了中英文报告配对问题!")
            print(f"✅ 确认了01760是INTRON TECH的正确股票代码!")
        else:
            print("跳过下载")


def main():
    """主函数"""
    scraper = HKEXFinalCorrectScraper()
    
    # 使用用户截图中显示的日期范围
    scraper.scrape_intron_tech_reports(
        from_date="01/01/2024",
        to_date="01/01/2025"
    )


if __name__ == "__main__":
    main()
