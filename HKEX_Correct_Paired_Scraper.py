"""
香港交易所PDF爬虫 - 正确配对版本
基于用户提供的Response片段，发现了正确的股票代码映射规律

关键发现:
- 01760 INTRON TECH 的正确PDF代码是 00806/00807 配对
- 英文版: 00806, 01008 (偶数代码)
- 中文版: 00807_c, 01009_c (奇数代码 + _c后缀)
- 配对关系: 英文代码+1 = 中文代码
"""

import os
import requests
from datetime import datetime, timedelta
from tqdm import tqdm
import json
import time


class HKEXCorrectPairedScraper:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)
        
        # 配置
        self.base_url = "https://www1.hkexnews.hk"
        self.download_dir = "downloads_correct_paired"
        self.paired_dir = os.path.join(self.download_dir, "paired_reports")
        
        # 创建下载目录
        os.makedirs(self.paired_dir, exist_ok=True)
        
        self.timeout = 30
        self.request_delay = 0.5
        
        # 正确的股票代码映射 (基于用户提供的Response片段)
        self.intron_tech_codes = {
            'english_codes': ['00806', '01008'],  # 英文版使用的代码
            'chinese_codes': ['00807', '01009'],  # 中文版使用的代码
        }
    
    def generate_date_range(self, start_date, end_date):
        """生成日期范围"""
        start = datetime.strptime(start_date, '%d/%m/%Y')
        end = datetime.strptime(end_date, '%d/%m/%Y')
        
        dates = []
        current = start
        while current <= end:
            dates.append(current)
            current += timedelta(days=1)
        
        return dates
    
    def construct_paired_pdf_urls(self, date, english_code, chinese_code):
        """构造配对的PDF URL"""
        year = date.strftime('%Y')
        month_day = date.strftime('%m%d')
        date_str = date.strftime('%Y%m%d')
        
        # 构造英文版和中文版URL
        english_url = f"{self.base_url}/listedco/listconews/sehk/{year}/{month_day}/{date_str}{english_code}.pdf"
        chinese_url = f"{self.base_url}/listedco/listconews/sehk/{year}/{month_day}/{date_str}{chinese_code}_c.pdf"
        
        return {
            'english': english_url,
            'chinese': chinese_url,
            'base_filename': f"{date_str}_{english_code}_{chinese_code}"
        }
    
    def check_pdf_exists(self, url):
        """检查PDF是否存在"""
        try:
            response = self.session.head(url, timeout=self.timeout)
            return response.status_code == 200
        except:
            return False
    
    def download_pdf(self, url, filepath):
        """下载PDF文件"""
        try:
            response = self.session.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                if total_size > 0:
                    with tqdm(total=total_size, unit='B', unit_scale=True, 
                             desc=os.path.basename(filepath)) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            
            print(f"✓ 下载成功: {filepath}")
            return True
            
        except Exception as e:
            print(f"✗ 下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False
    
    def scan_and_download_paired_reports(self, start_date="01/01/2024", end_date="31/12/2024"):
        """扫描并下载INTRON TECH的配对报告"""
        print("=" * 60)
        print("🏢 爬取INTRON TECH (01760) 的正确配对报告")
        print("📄 基于用户提供的Response片段发现的正确代码映射")
        print("=" * 60)
        
        print(f"\n🎯 目标公司: INTRON TECH / 英唐科技")
        print(f"📊 股票代码: 01760")
        print(f"📅 日期范围: {start_date} 到 {end_date}")
        print(f"🔢 英文PDF代码: {self.intron_tech_codes['english_codes']}")
        print(f"🔢 中文PDF代码: {self.intron_tech_codes['chinese_codes']}")
        
        # 生成日期范围
        dates = self.generate_date_range(start_date, end_date)
        print(f"📅 总共需要检查 {len(dates)} 个日期")
        
        all_paired_reports = []
        total_downloaded = 0
        
        # 遍历所有英文和中文代码配对
        for en_code, zh_code in zip(self.intron_tech_codes['english_codes'], 
                                   self.intron_tech_codes['chinese_codes']):
            
            print(f"\n🔍 扫描代码配对: {en_code} (英文) ↔ {zh_code} (中文)")
            
            paired_reports = []
            
            for date in tqdm(dates, desc=f"扫描 {en_code}/{zh_code}"):
                date_str = date.strftime('%Y-%m-%d')
                
                # 构造配对的URL
                urls = self.construct_paired_pdf_urls(date, en_code, zh_code)
                
                # 检查是否存在配对的PDF
                english_exists = self.check_pdf_exists(urls['english'])
                chinese_exists = self.check_pdf_exists(urls['chinese'])
                
                if english_exists and chinese_exists:
                    print(f"\n✓ 找到配对报告: {date_str} ({en_code}↔{zh_code})")
                    
                    # 创建配对目录
                    pair_dir = os.path.join(self.paired_dir, f"{date_str}_{en_code}_{zh_code}")
                    os.makedirs(pair_dir, exist_ok=True)
                    
                    # 下载英文版
                    en_filename = f"EN_{date.strftime('%Y%m%d')}_{en_code}.pdf"
                    en_filepath = os.path.join(pair_dir, en_filename)
                    
                    en_downloaded = False
                    if not os.path.exists(en_filepath):
                        en_downloaded = self.download_pdf(urls['english'], en_filepath)
                    else:
                        print(f"英文版已存在: {en_filename}")
                        en_downloaded = True
                    
                    # 下载中文版
                    zh_filename = f"ZH_{date.strftime('%Y%m%d')}_{zh_code}_c.pdf"
                    zh_filepath = os.path.join(pair_dir, zh_filename)
                    
                    zh_downloaded = False
                    if not os.path.exists(zh_filepath):
                        zh_downloaded = self.download_pdf(urls['chinese'], zh_filepath)
                    else:
                        print(f"中文版已存在: {zh_filename}")
                        zh_downloaded = True
                    
                    # 记录配对信息
                    if en_downloaded and zh_downloaded:
                        paired_reports.append({
                            'date': date_str,
                            'english_code': en_code,
                            'chinese_code': zh_code,
                            'english_url': urls['english'],
                            'chinese_url': urls['chinese'],
                            'english_file': en_filename,
                            'chinese_file': zh_filename,
                            'pair_directory': pair_dir,
                            'is_paired': True
                        })
                        total_downloaded += 1
                
                elif english_exists or chinese_exists:
                    # 只有一个版本存在
                    version = "英文" if english_exists else "中文"
                    url = urls['english'] if english_exists else urls['chinese']
                    print(f"\n⚠ 只找到{version}版本: {date_str} ({en_code}/{zh_code})")
                    
                    # 也下载单一版本
                    single_filename = f"SINGLE_{version}_{date.strftime('%Y%m%d')}_{en_code if english_exists else zh_code}.pdf"
                    single_filepath = os.path.join(self.paired_dir, single_filename)
                    
                    if not os.path.exists(single_filepath):
                        if self.download_pdf(url, single_filepath):
                            paired_reports.append({
                                'date': date_str,
                                'code': en_code if english_exists else zh_code,
                                'url': url,
                                'file': single_filename,
                                'is_paired': False,
                                'version': version
                            })
                
                time.sleep(self.request_delay)
            
            all_paired_reports.extend(paired_reports)
            print(f"\n📊 代码配对 {en_code}/{zh_code} 结果:")
            paired_count = len([r for r in paired_reports if r.get('is_paired', False)])
            single_count = len([r for r in paired_reports if not r.get('is_paired', False)])
            print(f"  配对报告: {paired_count} 对")
            print(f"  单一报告: {single_count} 个")
        
        print(f"\n🎉 扫描完成!")
        total_paired = len([r for r in all_paired_reports if r.get('is_paired', False)])
        total_single = len([r for r in all_paired_reports if not r.get('is_paired', False)])
        
        print(f"📊 总体结果:")
        print(f"  配对报告: {total_paired} 对")
        print(f"  单一报告: {total_single} 个")
        print(f"  总下载: {len(all_paired_reports)} 个文档")
        
        # 保存结果
        results = {
            'timestamp': datetime.now().isoformat(),
            'company': 'INTRON TECH / 英唐科技',
            'stock_code': '01760',
            'date_range': f"{start_date} to {end_date}",
            'code_mapping': self.intron_tech_codes,
            'total_paired_reports': total_paired,
            'total_single_reports': total_single,
            'total_documents': len(all_paired_reports),
            'reports': all_paired_reports
        }
        
        results_filename = f'intron_tech_correct_paired_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(results_filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细结果已保存到: {results_filename}")
        print(f"📁 配对报告保存在: {os.path.abspath(self.paired_dir)}")
        
        if total_paired > 0:
            print(f"\n✅ 成功解决了两个关键问题:")
            print(f"1. ✅ 找到了01760 INTRON TECH的正确PDF代码映射")
            print(f"2. ✅ 确保了中英文报告是同一份文档的不同语言版本")
        
        return all_paired_reports
    
    def test_known_dates(self):
        """测试已知存在PDF的日期"""
        print("🧪 测试已知日期的PDF存在性...")
        
        # 基于用户提供的Response片段中的日期
        known_dates = [
            datetime(2024, 12, 5),   # 2024/12/05 - 月报
            datetime(2024, 11, 29),  # 2024/11/29 - 董事名单
        ]
        
        for date in known_dates:
            print(f"\n测试日期: {date.strftime('%Y-%m-%d')}")
            
            for en_code, zh_code in zip(self.intron_tech_codes['english_codes'], 
                                       self.intron_tech_codes['chinese_codes']):
                
                urls = self.construct_paired_pdf_urls(date, en_code, zh_code)
                
                en_exists = self.check_pdf_exists(urls['english'])
                zh_exists = self.check_pdf_exists(urls['chinese'])
                
                print(f"  代码配对 {en_code}/{zh_code}:")
                print(f"    英文版: {'✓ 存在' if en_exists else '✗ 不存在'}")
                print(f"    中文版: {'✓ 存在' if zh_exists else '✗ 不存在'}")
                
                if en_exists and zh_exists:
                    print(f"    🎉 找到完整配对!")
                    print(f"    英文URL: {urls['english']}")
                    print(f"    中文URL: {urls['chinese']}")


def main():
    """主函数"""
    scraper = HKEXCorrectPairedScraper()
    
    print("🧪 首先测试已知日期...")
    scraper.test_known_dates()
    
    print(f"\n" + "="*50)
    choice = input("是否继续进行完整扫描? (y/n): ").lower().strip()
    
    if choice == 'y':
        # 询问日期范围
        print(f"\n📅 选择扫描范围:")
        print("1. 最近3个月")
        print("2. 整个2024年")
        print("3. 用户截图范围 (2024/01/01 - 2025/01/01)")
        print("4. 自定义范围")
        
        range_choice = input("请选择 (1-4): ").strip()
        
        if range_choice == '1':
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
        elif range_choice == '2':
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2024, 12, 31)
        elif range_choice == '3':
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2025, 1, 1)
        elif range_choice == '4':
            start_str = input("开始日期 (DD/MM/YYYY): ").strip()
            end_str = input("结束日期 (DD/MM/YYYY): ").strip()
            start_date = datetime.strptime(start_str, '%d/%m/%Y')
            end_date = datetime.strptime(end_str, '%d/%m/%Y')
        else:
            print("使用默认范围: 最近3个月")
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
        
        start_str = start_date.strftime('%d/%m/%Y')
        end_str = end_date.strftime('%d/%m/%Y')
        
        # 开始完整扫描
        paired_reports = scraper.scan_and_download_paired_reports(
            start_date=start_str,
            end_date=end_str
        )
    else:
        print("跳过完整扫描")


if __name__ == "__main__":
    main()
