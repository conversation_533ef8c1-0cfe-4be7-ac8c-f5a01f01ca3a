# 香港交易所PDF爬虫

这是一个专门用于爬取香港交易所新闻网站 (https://www.hkexnews.hk/) 中PDF文件的Python爬虫程序。该程序能够自动发现并下载英文版和繁体中文版的对应PDF文件。

## 功能特点

- 🔍 **智能搜索**: 支持多种搜索方式，包括标题搜索、目录浏览和直接链接发现
- 📄 **PDF配对**: 自动识别和配对英文版(_e.pdf)和繁体中文版(_c.pdf)的PDF文件
- 📁 **分类下载**: 自动将英文和中文PDF分别保存到不同目录
- 🔄 **重试机制**: 内置请求重试和错误处理机制
- 📊 **进度显示**: 实时显示下载进度
- ⚙️ **可配置**: 支持自定义搜索参数和下载设置

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖包
```bash
pip install -r requirements.txt
```

或手动安装：
```bash
pip install requests beautifulsoup4 lxml tqdm urllib3
```

## 使用方法

### 1. 快速开始

运行主程序：
```bash
python hkex_pdf_scraper.py
```

### 2. 运行测试

在开始使用前，建议先运行测试确保一切正常：
```bash
python test_scraper.py
```

### 3. 自定义配置

编辑 `config.py` 文件来修改配置：

```python
# 修改下载目录
DOWNLOAD_DIR = "my_downloads"

# 修改搜索参数
SEARCH_PARAMS = {
    'fromDate': '01/01/2024',  # 开始日期
    'toDate': '31/12/2024',    # 结束日期
    'title': '年报',           # 搜索关键词
    # ... 其他参数
}

# 修改请求延迟（避免过于频繁的请求）
REQUEST_DELAY = 2  # 秒
```

### 4. 编程方式使用

```python
from hkex_pdf_scraper import HKEXPDFScraper

# 创建爬虫实例
scraper = HKEXPDFScraper()

# 搜索PDF文件
pdf_links = scraper.search_documents(
    from_date="01/01/2024",
    to_date="31/12/2024",
    title_keyword="年报",
    max_pages=5
)

# 配对PDF文件
paired_pdfs = scraper.pair_pdfs(pdf_links)

# 下载PDF文件
scraper.download_pdf_pairs(
    paired_pdfs,
    download_english=True,
    download_chinese=True
)
```

## 文件结构

```
scraper_c4/
├── hkex_pdf_scraper.py    # 主爬虫程序
├── config.py              # 配置文件
├── utils.py               # 工具函数
├── test_scraper.py        # 测试程序
├── requirements.txt       # 依赖包列表
├── README.md             # 说明文档
└── downloads/            # 下载目录（自动创建）
    ├── english/          # 英文PDF文件
    └── traditional_chinese/  # 繁体中文PDF文件
```

## 配置说明

### 搜索参数

- `fromDate`: 搜索开始日期 (格式: dd/mm/yyyy)
- `toDate`: 搜索结束日期 (格式: dd/mm/yyyy)
- `title`: 标题关键词
- `category`: 文档类别 (0=所有类别)
- `market`: 市场 (SEHK=主板, GEM=创业板)
- `stockId`: 股票代码 (-1=所有股票)

### 下载设置

- `DOWNLOAD_DIR`: 主下载目录
- `REQUEST_DELAY`: 请求间隔时间（秒）
- `MAX_RETRIES`: 最大重试次数
- `TIMEOUT`: 请求超时时间（秒）

## 注意事项

1. **网络连接**: 确保网络连接稳定，能够访问香港交易所网站
2. **请求频率**: 程序内置了请求延迟，请勿过于频繁地请求以免被网站限制
3. **存储空间**: PDF文件可能较大，确保有足够的存储空间
4. **法律合规**: 请确保您的使用符合网站的使用条款和相关法律法规

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 增加 `TIMEOUT` 值
   - 检查防火墙设置

2. **找不到PDF文件**
   - 检查搜索日期范围
   - 尝试不同的搜索关键词
   - 确认网站结构没有变化

3. **下载失败**
   - 检查磁盘空间
   - 确认下载目录权限
   - 检查网络稳定性

### 调试模式

在代码中添加调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 更新日志

- v1.0.0: 初始版本，支持基本的PDF搜索和下载功能
- 支持英文和繁体中文PDF配对
- 内置多种搜索策略
- 完整的错误处理和重试机制

## 许可证

本项目仅供学习和研究使用。使用时请遵守香港交易所网站的使用条款。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
