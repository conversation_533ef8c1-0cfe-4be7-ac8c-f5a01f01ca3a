"""
香港交易所PDF爬虫 - 修正版
解决股票代码映射错误和中英文报告不匹配的问题

重要发现:
- Intron Technology Holdings Ltd. 的正确代码可能是 209176 (不是01760)
- 需要确保中英文PDF是同一份报告的不同语言版本
"""

import os
import requests
from datetime import datetime, timedelta
from tqdm import tqdm
import json


class HKEXCorrectedScraper:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)
        
        # 配置
        self.base_url = "https://www1.hkexnews.hk"
        self.download_dir = "downloads_corrected"
        self.paired_dir = os.path.join(self.download_dir, "paired_reports")
        self.timeout = 30
        
        # 创建下载目录
        os.makedirs(self.paired_dir, exist_ok=True)
        
        # 修正的股票代码映射
        self.stock_code_mapping = {
            # 基于HKEX公司列表的发现
            '01760': '209176',  # Intron Technology Holdings Ltd.
            '1760': '209176',
            '209176': '209176',
            
            # 保留之前错误的映射作为参考
            '00741': '00741',  # 这是另一家公司，不是Intron Technology
        }
    
    def get_pdf_code(self, display_code):
        """获取PDF文件中使用的股票代码"""
        return self.stock_code_mapping.get(display_code, display_code)
    
    def generate_date_range(self, start_date, end_date):
        """生成日期范围"""
        start = datetime.strptime(start_date, '%d/%m/%Y')
        end = datetime.strptime(end_date, '%d/%m/%Y')
        
        dates = []
        current = start
        while current <= end:
            dates.append(current)
            current += timedelta(days=1)
        
        return dates
    
    def construct_paired_pdf_urls(self, date, stock_code):
        """构造配对的PDF URL (确保是同一份报告的中英文版本)"""
        pdf_code = self.get_pdf_code(stock_code)
        
        year = date.strftime('%Y')
        month_day = date.strftime('%m%d')
        date_str = date.strftime('%Y%m%d')
        
        # 构造同一份报告的不同语言版本
        base_filename = f"{date_str}{pdf_code}"
        
        urls = {
            'english': f"{self.base_url}/listedco/listconews/sehk/{year}/{month_day}/{base_filename}_e.pdf",
            'chinese': f"{self.base_url}/listedco/listconews/sehk/{year}/{month_day}/{base_filename}_c.pdf",
            'default': f"{self.base_url}/listedco/listconews/sehk/{year}/{month_day}/{base_filename}.pdf",
        }
        
        return urls, base_filename
    
    def check_pdf_exists(self, url):
        """检查PDF是否存在"""
        try:
            response = self.session.head(url, timeout=self.timeout)
            return response.status_code == 200
        except:
            return False
    
    def download_pdf(self, url, filepath):
        """下载PDF文件"""
        try:
            response = self.session.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                if total_size > 0:
                    with tqdm(total=total_size, unit='B', unit_scale=True, 
                             desc=os.path.basename(filepath)) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            
            print(f"✓ 下载成功: {filepath}")
            return True
            
        except Exception as e:
            print(f"✗ 下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False
    
    def scan_and_download_paired_pdfs(self, stock_code, start_date="01/01/2024", end_date="31/12/2024"):
        """扫描并下载配对的PDF文件"""
        print(f"开始扫描股票 {stock_code} 的配对PDF文件...")
        print(f"公司: Intron Technology Holdings Ltd.")
        print(f"日期范围: {start_date} 到 {end_date}")
        print(f"PDF代码: {self.get_pdf_code(stock_code)}")
        
        # 生成日期范围
        dates = self.generate_date_range(start_date, end_date)
        print(f"总共需要检查 {len(dates)} 个日期")
        
        paired_reports = []
        downloaded_pairs = 0
        
        for date in tqdm(dates, desc="扫描日期"):
            date_str = date.strftime('%Y-%m-%d')
            
            # 构造配对的URL
            urls, base_filename = self.construct_paired_pdf_urls(date, stock_code)
            
            # 检查是否存在配对的PDF
            english_exists = self.check_pdf_exists(urls['english'])
            chinese_exists = self.check_pdf_exists(urls['chinese'])
            default_exists = self.check_pdf_exists(urls['default'])
            
            # 只处理真正配对的报告
            if english_exists and chinese_exists:
                print(f"\n找到配对报告: {date_str} (英文+中文)")
                
                # 创建配对目录
                pair_dir = os.path.join(self.paired_dir, f"{date_str}_{base_filename}")
                os.makedirs(pair_dir, exist_ok=True)
                
                # 下载英文版
                en_filename = f"english_{base_filename}_e.pdf"
                en_filepath = os.path.join(pair_dir, en_filename)
                
                if not os.path.exists(en_filepath):
                    if self.download_pdf(urls['english'], en_filepath):
                        pass
                else:
                    print(f"英文版已存在: {en_filename}")
                
                # 下载中文版
                zh_filename = f"chinese_{base_filename}_c.pdf"
                zh_filepath = os.path.join(pair_dir, zh_filename)
                
                if not os.path.exists(zh_filepath):
                    if self.download_pdf(urls['chinese'], zh_filepath):
                        pass
                else:
                    print(f"中文版已存在: {zh_filename}")
                
                # 记录配对信息
                paired_reports.append({
                    'date': date_str,
                    'base_filename': base_filename,
                    'english_url': urls['english'],
                    'chinese_url': urls['chinese'],
                    'english_file': en_filename,
                    'chinese_file': zh_filename,
                    'pair_directory': pair_dir
                })
                
                downloaded_pairs += 1
                
            elif default_exists:
                print(f"\n找到默认报告: {date_str} (可能是双语)")
                
                # 下载默认版本
                default_filename = f"default_{base_filename}.pdf"
                default_filepath = os.path.join(self.paired_dir, default_filename)
                
                if not os.path.exists(default_filepath):
                    if self.download_pdf(urls['default'], default_filepath):
                        pass
                else:
                    print(f"默认版本已存在: {default_filename}")
                
                # 记录单一报告信息
                paired_reports.append({
                    'date': date_str,
                    'base_filename': base_filename,
                    'default_url': urls['default'],
                    'default_file': default_filename,
                    'type': 'default_only'
                })
        
        print(f"\n扫描完成!")
        print(f"找到配对报告: {len([r for r in paired_reports if 'english_url' in r])} 对")
        print(f"找到单一报告: {len([r for r in paired_reports if 'type' == 'default_only'])} 个")
        print(f"总下载: {len(paired_reports)} 个报告")
        
        # 保存配对结果
        results = {
            'timestamp': datetime.now().isoformat(),
            'stock_code': stock_code,
            'pdf_code': self.get_pdf_code(stock_code),
            'company_name': 'Intron Technology Holdings Ltd.',
            'date_range': f"{start_date} to {end_date}",
            'total_paired_reports': len([r for r in paired_reports if 'english_url' in r]),
            'total_single_reports': len([r for r in paired_reports if 'type' == 'default_only']),
            'reports': paired_reports
        }
        
        results_filename = f'paired_reports_{stock_code}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(results_filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"详细结果已保存到: {results_filename}")
        print(f"配对报告保存在: {os.path.abspath(self.paired_dir)}")
        
        return paired_reports
    
    def test_stock_codes(self, test_codes):
        """测试多个股票代码"""
        print("测试股票代码映射...")
        
        test_date = datetime(2024, 11, 29)  # 使用一个已知有文档的日期
        
        for display_code in test_codes:
            pdf_code = self.get_pdf_code(display_code)
            print(f"\n测试: {display_code} -> {pdf_code}")
            
            urls, base_filename = self.construct_paired_pdf_urls(test_date, display_code)
            
            for lang, url in urls.items():
                exists = self.check_pdf_exists(url)
                status = "✓ 存在" if exists else "✗ 不存在"
                print(f"  {lang}: {status}")
                if exists:
                    print(f"    URL: {url}")


def main():
    """主函数"""
    print("=" * 60)
    print("🏢 香港交易所PDF爬虫 - 修正版")
    print("📄 解决股票代码映射和中英文配对问题")
    print("=" * 60)
    
    scraper = HKEXCorrectedScraper()
    
    # 测试不同的股票代码
    print("\n🔍 第一步：测试股票代码映射")
    test_codes = ['01760', '1760', '209176', '00741']
    scraper.test_stock_codes(test_codes)
    
    # 询问用户选择正确的代码
    print(f"\n📋 根据测试结果，请选择正确的股票代码:")
    print("1. 01760 (原始输入)")
    print("2. 209176 (从HKEX公司列表发现)")
    print("3. 00741 (之前错误使用的代码)")
    print("4. 自定义输入")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == '1':
        stock_code = '01760'
    elif choice == '2':
        stock_code = '209176'
    elif choice == '3':
        stock_code = '00741'
    elif choice == '4':
        stock_code = input("请输入股票代码: ").strip()
    else:
        print("无效选择，使用默认值 01760")
        stock_code = '01760'
    
    print(f"\n🎯 使用股票代码: {stock_code}")
    print(f"🔢 PDF代码: {scraper.get_pdf_code(stock_code)}")
    
    # 询问日期范围
    print(f"\n📅 选择扫描范围:")
    print("1. 最近1个月")
    print("2. 最近3个月")
    print("3. 整个2024年")
    print("4. 自定义范围")
    
    range_choice = input("请选择 (1-4): ").strip()
    
    if range_choice == '1':
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
    elif range_choice == '2':
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
    elif range_choice == '3':
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31)
    elif range_choice == '4':
        start_str = input("开始日期 (DD/MM/YYYY): ").strip()
        end_str = input("结束日期 (DD/MM/YYYY): ").strip()
        start_date = datetime.strptime(start_str, '%d/%m/%Y')
        end_date = datetime.strptime(end_str, '%d/%m/%Y')
    else:
        print("无效选择，使用最近3个月")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
    
    start_str = start_date.strftime('%d/%m/%Y')
    end_str = end_date.strftime('%d/%m/%Y')
    
    print(f"\n🚀 开始扫描配对报告...")
    print(f"📊 股票代码: {stock_code}")
    print(f"📅 日期范围: {start_str} 到 {end_str}")
    
    # 扫描并下载配对的PDF
    paired_reports = scraper.scan_and_download_paired_pdfs(
        stock_code=stock_code,
        start_date=start_str,
        end_date=end_str
    )
    
    print(f"\n🎉 扫描完成!")
    print(f"📁 配对报告保存在: {scraper.paired_dir}")
    
    if paired_reports:
        paired_count = len([r for r in paired_reports if 'english_url' in r])
        single_count = len([r for r in paired_reports if 'type' == 'default_only'])
        
        print(f"📊 结果统计:")
        print(f"  配对报告 (中英文): {paired_count} 对")
        print(f"  单一报告 (默认): {single_count} 个")
        print(f"  总计: {len(paired_reports)} 个报告")
        
        if paired_count > 0:
            print(f"\n✅ 成功解决中英文配对问题!")
            print(f"每对报告都保存在独立的目录中，确保是同一份报告的不同语言版本。")
    else:
        print(f"❌ 没有找到任何报告")
        print(f"可能的原因:")
        print(f"1. 股票代码 {stock_code} 不正确")
        print(f"2. 该时间段内没有发布报告")
        print(f"3. PDF文件命名格式不同")


if __name__ == "__main__":
    main()
