"""
简单的HKEX测试 - 直接访问搜索页面并保存HTML
"""

import requests
from bs4 import BeautifulSoup


def simple_test():
    """简单测试HKEX搜索"""
    print("简单测试HKEX搜索...")

    # 创建会话
    session = requests.Session()
    session.headers.update(
        {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}
    )

    # 搜索参数 - 注意股票ID应该是1760而不是01760
    url = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
    params = {
        "stockId": "1760",  # 修改：去掉前导零
        "fromDate": "01/01/2024",
        "toDate": "31/12/2024",
        "searchType": "1",
        "category": "0",
        "market": "SEHK",
        "documentType": "-1",
        "title": "",
        "lang": "EN",
    }

    print(f"访问: {url}")
    print(f"参数: {params}")

    try:
        response = session.get(url, params=params, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"最终URL: {response.url}")

        # 保存原始HTML
        with open("simple_test_response.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("HTML已保存到 simple_test_response.html")

        # 简单解析
        soup = BeautifulSoup(response.content, "html.parser")

        # 查找表格
        tables = soup.find_all("table")
        print(f"找到 {len(tables)} 个表格")

        # 查找链接
        links = soup.find_all("a", href=True)
        print(f"找到 {len(links)} 个链接")

        # 查找PDF链接
        pdf_links = [link for link in links if ".pdf" in link.get("href", "").lower()]
        print(f"找到 {len(pdf_links)} 个PDF链接")

        # 查找包含"01760"的文本
        text_with_code = soup.find_all(text=lambda t: t and "01760" in str(t))
        print(f"找到包含'01760'的文本: {len(text_with_code)} 个")

        # 查找包含"INTRON"的文本
        text_with_intron = soup.find_all(text=lambda t: t and "INTRON" in str(t))
        print(f"找到包含'INTRON'的文本: {len(text_with_intron)} 个")

        # 查找"Total records found"
        total_records = soup.find(text=lambda t: t and "Total records found" in str(t))
        if total_records:
            print(f"记录统计: {total_records.strip()}")

        print("测试完成!")

    except Exception as e:
        print(f"错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    simple_test()
