"""
搜索Intron Technology的股票代码
"""

def search_intron_in_html():
    """在HTML文件中搜索Intron相关信息"""
    try:
        with open('company_list.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        print("搜索包含'Intron'的行:")
        for i, line in enumerate(lines, 1):
            if 'intron' in line.lower():
                print(f"第{i}行: {line.strip()}")
                
                # 查看前后几行的上下文
                print("上下文:")
                start = max(0, i-3)
                end = min(len(lines), i+3)
                for j in range(start, end):
                    marker = ">>> " if j == i-1 else "    "
                    print(f"{marker}{j+1}: {lines[j].strip()}")
                print("-" * 50)
        
        # 搜索可能的股票代码模式
        print("\n搜索可能的股票代码模式:")
        import re
        
        # 查找类似股票代码的模式
        stock_patterns = [
            r'\b\d{4}\b',  # 4位数字
            r'\b0\d{4}\b', # 0开头的5位数字
            r'\b\d{5}\b',  # 5位数字
        ]
        
        for pattern in stock_patterns:
            matches = re.findall(pattern, content)
            if matches:
                unique_matches = list(set(matches))
                print(f"模式 {pattern}: {unique_matches[:10]}")  # 只显示前10个
        
    except Exception as e:
        print(f"搜索失败: {e}")


if __name__ == "__main__":
    search_intron_in_html()
