"""
配置文件 - 香港交易所PDF爬虫
"""

import os

# 基础配置
BASE_URL = "https://www1.hkexnews.hk"
SEARCH_URL = "https://www1.hkexnews.hk/search/titlesearch.xhtml"

# 下载配置
DOWNLOAD_DIR = "downloads"
EN_DIR = os.path.join(DOWNLOAD_DIR, "english")
TC_DIR = os.path.join(DOWNLOAD_DIR, "traditional_chinese")

# 请求配置
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Accept-Encoding": "gzip, deflate",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
}

# 爬取配置
REQUEST_DELAY = 1  # 请求间隔（秒）
MAX_RETRIES = 3  # 最大重试次数
TIMEOUT = 30  # 请求超时时间（秒）

# PDF文件模式
PDF_PATTERNS = {"english": "_e.pdf", "traditional_chinese": "_c.pdf"}

# 搜索参数
SEARCH_PARAMS = {
    "lang": "EN",  # 或 'TC' 为繁体中文
    "category": "0",  # 所有类别
    "market": "SEHK",  # 主板
    "stockId": "01760",  # 所有股票
    "documentType": "-1",  # 所有文档类型
    "fromDate": "01/01/2024",  # 开始日期 (格式: dd/mm/yyyy)
    "toDate": "31/12/2024",  # 结束日期 (格式: dd/mm/yyyy)
    "title": "",  # 标题关键词
    "searchType": "1",  # 搜索类型
}
