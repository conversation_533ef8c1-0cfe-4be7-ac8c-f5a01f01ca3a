"""
香港交易所PDF爬虫 - 最终解决方案
直接构造PDF下载链接，避免解析复杂的搜索结果页面
"""

import os
import re
import time
import requests
from datetime import datetime, timedelta
from tqdm import tqdm
import json


class HKEXFinalScraper:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)
        
        # 配置
        self.base_url = "https://www1.hkexnews.hk"
        self.download_dir = "downloads"
        self.en_dir = os.path.join(self.download_dir, "english")
        self.tc_dir = os.path.join(self.download_dir, "traditional_chinese")
        self.request_delay = 1
        self.timeout = 30
        
        # 创建下载目录
        os.makedirs(self.en_dir, exist_ok=True)
        os.makedirs(self.tc_dir, exist_ok=True)
    
    def generate_pdf_urls_by_pattern(self, stock_code, year=2024):
        """根据已知模式生成可能的PDF URL"""
        print(f"生成股票 {stock_code} 在 {year} 年的可能PDF链接...")
        
        pdf_candidates = []
        
        # 香港交易所PDF链接的常见模式
        # https://www1.hkexnews.hk/listedco/listconews/sehk/YYYY/MMDD/YYYYMMDDXXXXX.pdf
        # https://www1.hkexnews.hk/listedco/listconews/sehk/YYYY/MMDD/YYYYMMDDXXXXX_e.pdf
        # https://www1.hkexnews.hk/listedco/listconews/sehk/YYYY/MMDD/YYYYMMDDXXXXX_c.pdf
        
        # 生成日期范围
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)
        current_date = start_date
        
        while current_date <= end_date:
            date_str = current_date.strftime("%Y%m%d")
            month_day = current_date.strftime("%m%d")
            year_str = str(year)
            
            # 常见的文档编号模式
            for doc_num in range(1, 100):  # 每天最多100个文档
                doc_id = f"{date_str}{doc_num:05d}"
                
                # 生成可能的URL
                base_patterns = [
                    f"https://www1.hkexnews.hk/listedco/listconews/sehk/{year_str}/{month_day}/{doc_id}.pdf",
                    f"https://www1.hkexnews.hk/listedco/listconews/sehk/{year_str}/{month_day}/{doc_id}_e.pdf",
                    f"https://www1.hkexnews.hk/listedco/listconews/sehk/{year_str}/{month_day}/{doc_id}_c.pdf"
                ]
                
                for url in base_patterns:
                    pdf_candidates.append({
                        'url': url,
                        'filename': os.path.basename(url),
                        'date': current_date.strftime("%Y-%m-%d"),
                        'doc_id': doc_id
                    })
            
            # 移动到下一天，但跳过一些天以减少请求数量
            current_date += timedelta(days=7)  # 每周检查一次
        
        print(f"生成了 {len(pdf_candidates)} 个候选PDF链接")
        return pdf_candidates
    
    def verify_pdf_exists(self, pdf_info):
        """验证PDF是否存在"""
        try:
            response = self.session.head(pdf_info['url'], timeout=5)
            if response.status_code == 200:
                # 检查是否真的是PDF文件
                content_type = response.headers.get('content-type', '').lower()
                if 'pdf' in content_type or pdf_info['url'].endswith('.pdf'):
                    return True
            return False
        except:
            return False
    
    def find_existing_pdfs(self, stock_code, year=2024, max_check=1000):
        """查找实际存在的PDF文件"""
        print(f"查找股票 {stock_code} 在 {year} 年的实际PDF文件...")
        
        # 生成候选链接
        candidates = self.generate_pdf_urls_by_pattern(stock_code, year)
        
        # 限制检查数量
        candidates = candidates[:max_check]
        
        existing_pdfs = []
        
        print(f"验证前 {len(candidates)} 个候选链接...")
        
        for i, pdf_info in enumerate(candidates):
            if i % 100 == 0:
                print(f"  已检查 {i}/{len(candidates)} 个链接...")
            
            if self.verify_pdf_exists(pdf_info):
                existing_pdfs.append(pdf_info)
                print(f"  找到PDF: {pdf_info['filename']}")
            
            # 添加小延迟
            if i % 10 == 0:
                time.sleep(0.1)
        
        print(f"找到 {len(existing_pdfs)} 个实际存在的PDF文件")
        return existing_pdfs
    
    def pair_pdfs_by_filename(self, pdf_list):
        """根据文件名配对英文和中文PDF"""
        print("配对英文和中文PDF...")
        
        english_pdfs = {}
        chinese_pdfs = {}
        other_pdfs = []
        
        for pdf in pdf_list:
            filename = pdf['filename']
            
            if filename.endswith('_e.pdf'):
                # 英文版
                base_name = filename.replace('_e.pdf', '')
                english_pdfs[base_name] = pdf
            elif filename.endswith('_c.pdf'):
                # 中文版
                base_name = filename.replace('_c.pdf', '')
                chinese_pdfs[base_name] = pdf
            else:
                # 其他PDF
                other_pdfs.append(pdf)
        
        # 配对
        paired_pdfs = []
        for base_name in english_pdfs:
            if base_name in chinese_pdfs:
                paired_pdfs.append({
                    'id': base_name,
                    'english': english_pdfs[base_name],
                    'chinese': chinese_pdfs[base_name]
                })
        
        print(f"配对结果:")
        print(f"  英文PDF: {len(english_pdfs)} 个")
        print(f"  中文PDF: {len(chinese_pdfs)} 个")
        print(f"  其他PDF: {len(other_pdfs)} 个")
        print(f"  成功配对: {len(paired_pdfs)} 对")
        
        return paired_pdfs
    
    def download_file(self, url, filepath):
        """下载文件"""
        try:
            response = self.session.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                if total_size > 0:
                    with tqdm(total=total_size, unit='B', unit_scale=True, 
                             desc=os.path.basename(filepath)) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            
            print(f"下载完成: {filepath}")
            return True
            
        except Exception as e:
            print(f"下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False
    
    def download_pdf_pairs(self, paired_pdfs, download_english=True, download_chinese=True):
        """下载配对的PDF文件"""
        print(f"开始下载 {len(paired_pdfs)} 对PDF文件...")
        
        successful_downloads = 0
        
        for i, pair in enumerate(paired_pdfs, 1):
            print(f"\n处理第 {i}/{len(paired_pdfs)} 对PDF (ID: {pair['id']})")
            
            pair_success = True
            
            # 下载英文版
            if download_english:
                en_pdf = pair['english']
                en_filename = f"en_{i:03d}_{en_pdf['filename']}"
                en_filepath = os.path.join(self.en_dir, en_filename)
                
                if os.path.exists(en_filepath):
                    print(f"英文版已存在: {en_filename}")
                else:
                    print(f"下载英文版: {en_pdf['filename']}")
                    if not self.download_file(en_pdf['url'], en_filepath):
                        pair_success = False
            
            # 下载中文版
            if download_chinese:
                zh_pdf = pair['chinese']
                zh_filename = f"zh_{i:03d}_{zh_pdf['filename']}"
                zh_filepath = os.path.join(self.tc_dir, zh_filename)
                
                if os.path.exists(zh_filepath):
                    print(f"中文版已存在: {zh_filename}")
                else:
                    print(f"下载中文版: {zh_pdf['filename']}")
                    if not self.download_file(zh_pdf['url'], zh_filepath):
                        pair_success = False
            
            if pair_success:
                successful_downloads += 1
            
            # 添加延迟
            if i < len(paired_pdfs):
                time.sleep(self.request_delay)
        
        print(f"\n下载完成! 成功下载 {successful_downloads}/{len(paired_pdfs)} 对PDF文件")
        return successful_downloads
    
    def save_results(self, paired_pdfs, filename="final_results.json"):
        """保存结果"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'total_pairs': len(paired_pdfs),
            'pairs': paired_pdfs
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到: {filename}")


def main():
    """主函数"""
    print("香港交易所PDF爬虫 - 最终解决方案")
    print("直接查找和下载真实的PDF文件")
    print("=" * 50)
    
    scraper = HKEXFinalScraper()
    
    # 从config.py读取参数或使用默认值
    try:
        import config
        search_params = config.SEARCH_PARAMS
        stock_code = search_params.get('stockId', '00700')
    except:
        stock_code = "00700"  # 腾讯控股
    
    print(f"目标股票代码: {stock_code}")
    
    # 查找实际存在的PDF文件
    existing_pdfs = scraper.find_existing_pdfs(stock_code, year=2024, max_check=500)
    
    if not existing_pdfs:
        print("没有找到任何PDF文件")
        print("提示: 可能需要调整股票代码或年份")
        return
    
    # 配对PDF
    paired_pdfs = scraper.pair_pdfs_by_filename(existing_pdfs)
    
    if not paired_pdfs:
        print("没有找到配对的PDF文件")
        print("但找到了以下单独的PDF文件:")
        for pdf in existing_pdfs[:10]:
            print(f"  - {pdf['filename']} ({pdf['date']})")
        return
    
    # 显示配对结果
    print(f"\n找到 {len(paired_pdfs)} 对配对的PDF文件:")
    for i, pair in enumerate(paired_pdfs[:5], 1):
        print(f"  {i}. {pair['id']}")
        print(f"     英文: {pair['english']['filename']}")
        print(f"     中文: {pair['chinese']['filename']}")
    
    if len(paired_pdfs) > 5:
        print(f"  ... 还有 {len(paired_pdfs) - 5} 对")
    
    # 保存结果
    scraper.save_results(paired_pdfs)
    
    # 询问是否下载
    download = input("\n是否开始下载? (y/n): ").lower().strip()
    
    if download == 'y':
        # 下载PDF
        scraper.download_pdf_pairs(paired_pdfs)
        print("\n🎉 下载完成!")
        print(f"英文PDF保存在: {scraper.en_dir}")
        print(f"中文PDF保存在: {scraper.tc_dir}")
    else:
        print("跳过下载")


if __name__ == "__main__":
    main()
