"""
查找INTRON TECH的正确信息
通过多种方式验证01760是否对应INTRON TECH
"""

import requests
from bs4 import BeautifulSoup
import time


def search_hkex_company_list():
    """搜索HKEX公司列表"""
    print("搜索HKEX公司列表...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # 尝试访问公司选择页面
    try:
        url = "https://di.hkex.com.hk/di/NSSelectCorp.aspx?src=MAIN&lang=EN&g_lang=en"
        print(f"访问公司列表: {url}")
        
        response = session.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            with open('company_list.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("公司列表已保存到 company_list.html")
            
            # 搜索INTRON相关信息
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找包含INTRON的文本
            intron_elements = soup.find_all(text=lambda t: t and 'INTRON' in str(t).upper())
            print(f"找到包含INTRON的文本: {len(intron_elements)} 个")
            
            for i, element in enumerate(intron_elements):
                print(f"  {i+1}. {element.strip()}")
            
            # 查找包含01760的文本
            code_elements = soup.find_all(text=lambda t: t and '01760' in str(t))
            print(f"找到包含01760的文本: {len(code_elements)} 个")
            
            for i, element in enumerate(code_elements):
                print(f"  {i+1}. {element.strip()}")
                
    except Exception as e:
        print(f"访问公司列表失败: {e}")


def search_google_for_intron_tech():
    """通过Google搜索INTRON TECH的信息"""
    print("\n通过网络搜索INTRON TECH信息...")
    
    # 这里我们可以提供一些已知的信息来帮助用户
    print("建议手动搜索以下关键词来验证:")
    print("1. 'INTRON TECH 01760 Hong Kong Stock Exchange'")
    print("2. 'INTRON TECH HK stock code'")
    print("3. '01760 HKEX listed company'")
    print("4. 'INTRON TECH 香港交易所'")


def test_alternative_stock_codes():
    """测试可能的替代股票代码"""
    print("\n测试可能的替代股票代码...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # 如果01760不是INTRON TECH，那么INTRON TECH可能有其他代码
    # 让我们测试一些可能的代码
    possible_codes = [
        "01760",  # 原始代码
        "1760",   # 去掉前导零
        "0176",   # 4位代码
        "176",    # 3位代码
        "01761",  # 相邻代码
        "01759",  # 相邻代码
        "1761",   # 相邻代码
        "1759",   # 相邻代码
    ]
    
    base_url = "https://www1.hkexnews.hk/listedco/listconews/sehk"
    test_date = "2024/1129"
    date_str = "20241129"
    
    print(f"测试日期: {test_date}")
    
    for code in possible_codes:
        print(f"\n测试代码: {code}")
        
        # 构造可能的URL
        test_urls = [
            f"{base_url}/{test_date}/{date_str}{code.zfill(5)}.pdf",  # 补齐到5位
            f"{base_url}/{test_date}/{date_str}{code.zfill(4)}.pdf",  # 补齐到4位
        ]
        
        for url in test_urls:
            try:
                response = session.head(url, timeout=10)
                if response.status_code == 200:
                    print(f"  ✓ 存在: {url}")
                else:
                    print(f"  ✗ {response.status_code}: {url}")
                    
                time.sleep(0.2)
                
            except Exception as e:
                print(f"  ✗ 错误: {url}")


def analyze_downloaded_pdfs():
    """分析已下载的PDF文件，看看是哪家公司的"""
    print("\n分析已下载的PDF文件...")
    
    import os
    
    # 检查下载目录
    download_dirs = [
        "downloads/default",
        "downloads/chinese"
    ]
    
    for dir_path in download_dirs:
        if os.path.exists(dir_path):
            files = os.listdir(dir_path)
            print(f"\n{dir_path} 目录中的文件:")
            
            for file in files[:3]:  # 只显示前3个文件
                file_path = os.path.join(dir_path, file)
                file_size = os.path.getsize(file_path)
                print(f"  {file} ({file_size} bytes)")
            
            if len(files) > 3:
                print(f"  ... 还有 {len(files) - 3} 个文件")
            
            # 建议用户手动检查PDF内容
            if files:
                print(f"  建议手动打开 {files[0]} 查看公司名称")


def check_hkex_official_sources():
    """检查HKEX官方数据源"""
    print("\n检查HKEX官方数据源...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # 尝试访问HKEX的官方股票信息页面
    official_urls = [
        "https://www.hkex.com.hk/Market-Data/Securities-Prices/Equities?sc_lang=en",
        "https://www.hkex.com.hk/Listing/Listed-Securities/Listed-Companies?sc_lang=en",
        "https://www.hkex.com.hk/eng/index.htm",
    ]
    
    for url in official_urls:
        try:
            print(f"\n尝试访问: {url}")
            response = session.get(url, timeout=30)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 检查是否包含01760或INTRON
                content = response.text.upper()
                
                if '01760' in content:
                    print("✓ 页面包含01760")
                else:
                    print("✗ 页面不包含01760")
                
                if 'INTRON' in content:
                    print("✓ 页面包含INTRON")
                else:
                    print("✗ 页面不包含INTRON")
                    
        except Exception as e:
            print(f"访问失败: {e}")
        
        time.sleep(1)


def main():
    """主函数"""
    print("查找INTRON TECH的正确信息")
    print("="*50)
    
    # 1. 搜索HKEX公司列表
    search_hkex_company_list()
    
    # 2. 测试替代股票代码
    test_alternative_stock_codes()
    
    # 3. 分析已下载的PDF
    analyze_downloaded_pdfs()
    
    # 4. 检查官方数据源
    check_hkex_official_sources()
    
    # 5. 提供搜索建议
    search_google_for_intron_tech()
    
    print("\n" + "="*50)
    print("总结和建议:")
    print("1. 请手动打开已下载的PDF文件，查看实际的公司名称")
    print("2. 如果PDF中的公司不是INTRON TECH，说明01760不是正确的代码")
    print("3. 请确认INTRON TECH的正确股票代码")
    print("4. 可能需要搜索'INTRON TECH'的官方信息")
    print("5. 检查是否有其他名称，如'INTRON TECHNOLOGY'等")


if __name__ == "__main__":
    main()
