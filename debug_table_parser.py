"""
调试表格解析器 - 专门用来调试HKEX搜索结果表格
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import json


def debug_search_page():
    """调试搜索页面"""
    print("调试HKEX搜索结果表格解析...")
    
    session = requests.Session()
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Referer': 'https://www1.hkexnews.hk/',
    }
    session.headers.update(headers)
    
    # 搜索参数
    search_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
    search_params = {
        'lang': 'EN',
        'category': '0',
        'market': 'SEHK',
        'stockId': '01760',
        'documentType': '-1',
        'fromDate': '01/01/2024',
        'toDate': '31/12/2024',
        'title': '',
        'searchType': '1',
        'currentPageNo': '1'
    }
    
    print(f"搜索URL: {search_url}")
    print(f"搜索参数: {search_params}")
    
    try:
        # 发送请求
        print("\n发送搜索请求...")
        response = session.get(search_url, params=search_params, timeout=30)
        response.raise_for_status()
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应URL: {response.url}")
        print(f"响应内容长度: {len(response.content)} 字节")
        
        # 解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 保存HTML用于调试
        with open('debug_response.html', 'w', encoding='utf-8') as f:
            f.write(soup.prettify())
        print("HTML响应已保存到 debug_response.html")
        
        # 查找所有表格
        tables = soup.find_all('table')
        print(f"\n找到 {len(tables)} 个表格:")
        
        for i, table in enumerate(tables):
            print(f"\n=== 表格 {i+1} ===")
            
            # 查找表头
            headers = table.find_all('th')
            if headers:
                header_texts = [th.get_text(strip=True) for th in headers]
                print(f"表头: {header_texts}")
            else:
                print("没有找到表头")
            
            # 查找所有行
            rows = table.find_all('tr')
            print(f"总行数: {len(rows)}")
            
            # 显示前几行的内容
            for j, row in enumerate(rows[:5]):  # 只显示前5行
                cells = row.find_all(['td', 'th'])
                cell_texts = [cell.get_text(strip=True)[:30] for cell in cells]
                print(f"  第{j+1}行 ({len(cells)}列): {cell_texts}")
                
                # 查找链接
                links = row.find_all('a', href=True)
                if links:
                    print(f"    链接:")
                    for k, link in enumerate(links):
                        href = link['href']
                        link_text = link.get_text(strip=True)[:30]
                        print(f"      {k+1}. {href} -> {link_text}")
            
            if len(rows) > 5:
                print(f"  ... 还有 {len(rows) - 5} 行")
        
        # 查找特定的结果表格
        print(f"\n=== 查找结果表格 ===")
        result_table = None
        
        for table in tables:
            headers = table.find_all('th')
            header_texts = [th.get_text(strip=True) for th in headers]
            
            # 检查是否包含预期的列
            if any('Document' in header or 'Release Time' in header or 'Stock Code' in header for header in header_texts):
                print(f"找到结果表格! 列标题: {header_texts}")
                result_table = table
                break
        
        if result_table:
            print("\n=== 解析结果表格 ===")
            rows = result_table.find_all('tr')
            
            for i, row in enumerate(rows):
                if i == 0:  # 跳过表头
                    continue
                
                cells = row.find_all(['td', 'th'])
                print(f"\n第{i}行 ({len(cells)}列):")
                
                for j, cell in enumerate(cells):
                    cell_text = cell.get_text(strip=True)
                    print(f"  列{j+1}: {cell_text[:50]}")
                    
                    # 查找链接
                    links = cell.find_all('a', href=True)
                    if links:
                        for k, link in enumerate(links):
                            href = link['href']
                            link_text = link.get_text(strip=True)
                            print(f"    链接{k+1}: {href} -> {link_text[:30]}")
                
                if i >= 3:  # 只显示前3行数据
                    print(f"  ... 还有 {len(rows) - 4} 行数据")
                    break
        else:
            print("没有找到结果表格")
        
        # 查找所有链接
        print(f"\n=== 所有链接统计 ===")
        all_links = soup.find_all('a', href=True)
        print(f"页面总链接数: {len(all_links)}")
        
        pdf_links = [link for link in all_links if '.pdf' in link['href'].lower()]
        doc_links = [link for link in all_links if 'listedco' in link['href'] or 'listconews' in link['href']]
        
        print(f"PDF链接: {len(pdf_links)} 个")
        print(f"文档页面链接: {len(doc_links)} 个")
        
        if pdf_links:
            print("PDF链接示例:")
            for i, link in enumerate(pdf_links[:3]):
                print(f"  {i+1}. {link['href']} -> {link.get_text(strip=True)[:30]}")
        
        if doc_links:
            print("文档页面链接示例:")
            for i, link in enumerate(doc_links[:3]):
                print(f"  {i+1}. {link['href']} -> {link.get_text(strip=True)[:30]}")
        
        # 查找特定文本
        print(f"\n=== 查找特定内容 ===")
        
        # 查找"Total records found"
        total_text = soup.find(text=lambda text: text and 'Total records found' in text)
        if total_text:
            print(f"找到记录统计: {total_text.strip()}")
        
        # 查找"INTRON TECH"
        intron_elements = soup.find_all(text=lambda text: text and 'INTRON TECH' in text)
        print(f"找到 INTRON TECH 相关文本: {len(intron_elements)} 个")
        
        # 查找"01760"
        code_elements = soup.find_all(text=lambda text: text and '01760' in text)
        print(f"找到股票代码 01760: {len(code_elements)} 个")
        
    except Exception as e:
        print(f"调试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_search_page()
