"""
完整的HKEX测试 - 模拟真实浏览器行为
"""

import requests
from bs4 import BeautifulSoup
import time


def full_test():
    """完整测试HKEX搜索流程"""
    print("完整测试HKEX搜索流程...")
    
    # 创建会话
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    })
    
    try:
        # 第1步：访问主页面获取会话
        print("第1步：访问HKEX主页...")
        main_url = "https://www1.hkexnews.hk/"
        response = session.get(main_url, timeout=30)
        print(f"主页状态码: {response.status_code}")
        
        # 第2步：访问搜索页面
        print("第2步：访问搜索页面...")
        search_page_url = "https://www1.hkexnews.hk/search/titlesearch.xhtml"
        response = session.get(search_page_url, timeout=30)
        print(f"搜索页面状态码: {response.status_code}")
        
        # 解析搜索页面获取必要的参数
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找ViewState
        viewstate = soup.find('input', {'name': 'javax.faces.ViewState'})
        viewstate_value = viewstate['value'] if viewstate else None
        print(f"ViewState: {viewstate_value[:50] if viewstate_value else 'None'}...")
        
        # 第3步：执行搜索
        print("第3步：执行搜索...")
        
        # 尝试不同的搜索参数组合
        search_attempts = [
            {
                'name': '基本搜索',
                'params': {
                    'stockId': '1760',
                    'fromDate': '01/01/2024',
                    'toDate': '31/12/2024',
                    'searchType': '1'
                }
            },
            {
                'name': '完整参数搜索',
                'params': {
                    'stockId': '1760',
                    'fromDate': '01/01/2024',
                    'toDate': '31/12/2024',
                    'searchType': '1',
                    'category': '0',
                    'market': 'SEHK',
                    'documentType': '-1',
                    'title': '',
                    'lang': 'EN'
                }
            },
            {
                'name': '使用01760格式',
                'params': {
                    'stockId': '01760',
                    'fromDate': '01/01/2024',
                    'toDate': '31/12/2024',
                    'searchType': '1'
                }
            },
            {
                'name': '更大日期范围',
                'params': {
                    'stockId': '1760',
                    'fromDate': '01/01/2020',
                    'toDate': '31/12/2024',
                    'searchType': '1'
                }
            }
        ]
        
        for attempt in search_attempts:
            print(f"\n尝试: {attempt['name']}")
            print(f"参数: {attempt['params']}")
            
            response = session.get(search_page_url, params=attempt['params'], timeout=30)
            print(f"状态码: {response.status_code}")
            print(f"最终URL: {response.url}")
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找记录统计
            total_records = soup.find(string=lambda t: t and 'Total records found' in str(t))
            if total_records:
                print(f"结果: {total_records.strip()}")
                
                # 如果找到记录，保存HTML并分析
                if 'Total records found: 0' not in total_records:
                    filename = f"success_{attempt['name'].replace(' ', '_')}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"成功! HTML已保存到 {filename}")
                    
                    # 分析表格
                    tables = soup.find_all('table')
                    for table in tables:
                        rows = table.find_all('tr')
                        if len(rows) > 1:  # 有数据行
                            print(f"找到数据表格，共 {len(rows)-1} 行数据")
                            
                            # 显示前几行
                            for i, row in enumerate(rows[1:6]):  # 显示前5行数据
                                cells = row.find_all(['td', 'th'])
                                cell_texts = [cell.get_text(strip=True)[:30] for cell in cells]
                                print(f"  第{i+1}行: {cell_texts}")
                    
                    return True
            else:
                print("未找到记录统计")
            
            time.sleep(1)  # 避免请求过快
        
        print("\n所有尝试都没有找到记录")
        
        # 第4步：尝试直接访问已知的文档URL
        print("\n第4步：尝试直接访问已知文档...")
        
        # 从您的截图中，我们知道有文档存在，让我们尝试不同的方法
        # 尝试访问disclosure页面
        disclosure_url = "https://di.hkex.com.hk/di/NSSrchCorpQW.aspx"
        disclosure_params = {
            'src': 'MAIN',
            'lang': 'EN',
            'in': '1',
            'sc': '01760'
        }
        
        print(f"尝试访问disclosure页面: {disclosure_url}")
        response = session.get(disclosure_url, params=disclosure_params, timeout=30)
        print(f"Disclosure页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            with open('disclosure_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("Disclosure页面已保存到 disclosure_page.html")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    full_test()
