<!DOCTYPE html>
<html class="news-hkex" xmlns="http://www.w3.org/1999/xhtml"><head id="j_idt2">
    <title>上市公司信息標題搜尋</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="format-detection" content="telephone=no" />
    <link href="/ncms/images/favicon.ico" rel="icon" />
    <link href="/ncms/css/main.css" rel="stylesheet" />
    <link href="/ncms/css/titlesearch.css" rel="stylesheet" />
	
    <script type="text/javascript" src="/ncms/script/hkex_app.js"></script>
    <script type="text/javascript" src="/ncms/script/hkex_settings.js"></script>
    <script type="text/javascript" src="/ncms/script/hkex_widget.js"></script>  
    
    <script type="text/javascript">
			document.documentElement.setAttribute("lang","zh-HK");
		 	
	</script></head><body>
<form id="j_idt10" name="j_idt10" method="post" action="/search/titlesearch.xhtml" enctype="application/x-www-form-urlencoded" style="display: none;">
<input type="hidden" name="j_idt10" value="j_idt10" />
<input id="j_idt10:loadMoreRange" type="hidden" name="j_idt10:loadMoreRange" value="100" />
<input id="startDate" type="hidden" />
<input id="endDate" type="hidden" />
<input id="stockId" type="hidden" />
<input id="stockCode" type="hidden" />
<input id="searchType" type="hidden" />
<input id="searchTypeInt" type="hidden" />
<input id="newsTitle" type="hidden" />
<input id="tierOneId" type="hidden" />
<input id="tierTwoId" type="hidden" />
<input id="tierTwoGpId" type="hidden" />
<input id="selectedDocType" type="hidden" />
<input id="selectedSecurities" type="hidden" />
<input id="displayResultTable" type="hidden" value="none" /><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="-3997118555769376916:-2248819195897021425" autocomplete="off" />
</form>
	<input type="hidden" name="titleSearchResultControl.searchByIndex" value="0" />
	<input type="hidden" name="titleSearchByAllResult.dateFromUi" />
	<input type="hidden" name="titleSearchByAllResult.dateToUi" />

	<script type="text/javascript" src="/ncms/script/vendor.js"></script>
		 <script type="text/javascript" src="/ncms/script/hkex_head_c.js"></script>
    
    
    <div id="skip-to-content"></div>
	
    
	<div id="hkex_news_topbanner">
        <section class="banner banner__mobile_breadcrumb banner__head">
            <div class="tab-content">
		    	<div data-include-html="titleSearchSearchPanel_c.html" role="tabpanel" class="tab-pane active" id="tab-panel-title-search"></div>
		    </div>
        </section>
    </div>
	
	
	
	<script type="text/javascript">
	    var displayResultTable = $('input#displayResultTable').val();
		if(displayResultTable == ''){
			// JS variables, please set before hkex_pagebanner.js
		   var pageDefaultTitle = "上市公司公告搜尋結果";	
		    var pageDefaultSubTitle = ""; // TO override page subtitle
		    var pageDefaultBanner = "/ncms/media/HKEXnews/top_banner_bg.png"; // TO override page banner for desktop
		    var pageDefaultTabletBanner = "/ncms/media/HKEXnews/top_banner_bg_tablet.png"; // TO override page banner for tablet
		}
	</script>
		    <script type="text/javascript" src="/ncms/script/hkex_pagebanner_c.js"></script>
    

    
    <div class="container content-container title-search-container content-container-reset table-mobile-list-container" style="display:  none">
        <input id="LISTING_CAT_LINK" type="hidden" value="https://www.hkex.com.hk/eng/rulesreg/listrules/listsptop/listoc/co_inf.htm" style="display: none;" />
        <input id="lang" type="hidden" value="C" style="display: none;" />
        <main class="title-search"><div id="categoryPanel"> 
            <div class="title-search-topbar">
                <div class="title-search-info">
                    <div class="quick-links">
                        <ul>
                        
						   <li><a title="披露權益">披露權益</a></li>
                        </ul>
                    </div><div id="recordCountPanel">
                    <div class="title-search-info-footer clearfix">
                        <div class="total-records">共有 0 紀錄</div>
                    	<div class="disclaimer">
                            <a href="https://www2.hkexnews.hk/-/media/HKEXnews/Homepage/Listed-Company-Publications/Search-Guide/SearchDisclaimer_c.pdf" class="fancy-note-label" data-toggle="popup" data-target="#cms-lci-disclaimer">免責聲明</a>
                        </div>
                    </div></div>
                </div>
            </div></div><div id="titleSearchResultPanel"> 
            <div class="title-search-content">            
                <div class="title-search-result search-page-container">
                    <div class="title-search-result-toolbar result-toolbar">
                        <div class="hideHeadline">
                            <a tabindex="0" class="hideShow showing"><span class="hide-headline">隱藏標題</span><span class="show-headline">顯示標題</span></a>
                        </div>
                        <div class="mobileSort">
                            <a tabindex="0" data-toggle="nav-popover" class="sort__dropdown">排序</a>
                            <div class="popover result-toolbar-popover">
                                <div class="popover__content">
                                    <ul class="popover__content-list">
                                        <li class="popover__list-item" onclick="javascript:sortByReleaseTimeDesc()"><a tabindex="0" data-sort-by="0" data-sort-dir="1">最新 資訊</a></li>
                                        <li class="popover__list-item" onclick="javascript:sortByReleaseTimeAsc()"><a tabindex="0" data-sort-by="0" data-sort-dir="0">最舊 資訊</a></li>
                                        <li class="popover__list-item" onclick="javascript:sortByStockCodeAsc()"><a tabindex="0" data-sort-by="1" data-sort-dir="0">代號 升序排列</a></li>
                                        <li class="popover__list-item" onclick="javascript:sortByStockCodeDesc()"><a tabindex="0" data-sort-by="1" data-sort-dir="1">代號 降序排列</a></li>
                                        <li class="popover__list-item" onclick="javascript:sortByStockNameAsc()"><a tabindex="0" data-sort-by="2" data-sort-dir="0">股份名稱 升序排列</a></li>
                                        <li class="popover__list-item" onclick="javascript:sortByStockNameDesc()"><a tabindex="0" data-sort-by="2" data-sort-dir="1">股份名稱 降序排列</a></li> 
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="title-search-result-mobile desktop-hidden tablet-hidden">
                    </div>
                    
                    <table class="table sticky-header-table table-scroll table-mobile-list" role="grid" data-sortlist="[[0,1]]">
                        <thead>
                            <tr>
                                <th class="release-date" data-column-class="release-date" data-sorter="extractDDMMYYYY" data-date-format="ddmmyyyy" onclick="javascript:sortTable('DateTime')">發放時間</th>
                                <th class="stock-code" data-column-class="stock-code" data-sorter="text" onclick="javascript:sortTable('StockCode')">股份代號</th>
                                <th class="stock-code-name" data-column-class="stock-code-name" onclick="javascript:sortTable('StockName')">股份簡稱</th>
                                <th class="sorter-false document-details headline-toggle" data-column-class="document-details">文件</th>
                            </tr>
                        </thead>
                        <tbody>                      
                       </tbody>
                   </table>
                  
                </div><div id="recordCountPanel2">
                 <div class="search-results__content-loadmore component-loadmore component-loadmore-no-options">
                    <div class="component-loadmore__container">
                        <div class="result_norecords">
                        	沒有找到訊息
                        </div>
                    </div>
                </div></div>
            </div></div>
        </main>
    </div>     
    
    <div class="fancy-popup-wrap fancy-note">
        <div>
            
            <div class="fancy-note-popup" id="cms-lci-disclaimer">
                <a class="fancy-note-close" href="#"></a>
                <div class="fancy-note-detail">
                    <div class="fancy-note-title">免責聲明</div>
                    <div class="fancy-note-desc">
                        <p>載有本免責聲明的超連結的網頁所顯示的文件及資料在本網站上登載，目的是向香港公眾人士提供資料；其中的內容並非投資建議，不應倚賴其作為投資建議。在香港以外地區的人士在瀏覽及使用該等文件及資料前，須自行查閱及遵守所有適用法例及規定。                            
                        </p>
                        <p>請注意：該網頁載有由上市發行人、新上市申請人及有關人士以電子方式 (包括以電子格式提供的資料) 向香港交易所提供的文件及資料，而香港交易所與上述人士之間並無任何代理、僱員或合夥關係。香港交易所並不負責審查、編輯或監察該等文件及當中所載資料，有關文件內的資料全部未經香港交易所核實。
                        </p>
                        <p>香港交易所及/或其附屬公司對通過該網頁而取得的該等文件及資料的內容概不負責，對其準確性、可靠性、完整性或擁有權亦不發表任何聲明，並明確表示概不對任何因該等資料全部或任何部分內容而產生或因倚賴該等內容而引致的損失承擔任何責任。另外，有些文件及資料可能包含有連接到外部網站或網頁的超連結，香港交易所及/或其附屬公司對可否成功連接到有關網站或網頁及其所載內容亦概不負責。(見香港交易所「<a href="http://www2.hkexnews.hk/Global/Exchange/Hyperlink-Policy?sc_lang=zh-HK" target="_blank">超連結政策</a>」)。
                        </p>
                        <p>本聲明的內容概不構成或被視為香港交易所及其附屬公司放棄其專有的特權及豁免權又或對上述權力構成任何限制。
                        </p>
                        <p>通過該網頁而取得的資料及文件均具有版權，可能屬香港交易所、作者或任何其他人士所有，視乎刊物的情況而定。未經有關當事人同意，任何人士不得轉載、發布、使用及／或連結該等內容。
                        </p>
                        <p>如得悉或相信該等文件所載的任何資料不準確、錯誤、不合法、淫褻、誹謗或侵犯第三者的知識產權，請聯絡【香港交易所電子呈交小組】(HKEX e-Submission)，電郵地址： <a href="mailto:<EMAIL>"><EMAIL></a>。
                        </p>
                    </div>
                </div>
            </div>
            <div class="fancy-note-shade"></div>
        </div>
    </div>
    
    
    <div class="overlay"></div>
    	<script type="text/javascript" src="/ncms/script/hkex_foot_c.js"></script>
    
    
	<script type="text/javascript">
    (function($) {
		includeHTML();
	})(window.hkexCMS);
    
    </script>
    <script type="text/javascript" src="/ncms/script/main.js"></script>
    <script type="text/javascript" src="/ncms/eds/titlesearch/config_c.js"></script>
    <script type="text/javascript" src="/ncms/js/titlesearch.js"></script>
    <script type="text/javascript" src="/ncms/js/titlesearch_research.js"></script>
  	<script type="text/javascript" src="/ncms/js/search.js"></script>
  	<script type="text/javascript"> 
    TitlseSearchConfig();
    </script></body>

</html>