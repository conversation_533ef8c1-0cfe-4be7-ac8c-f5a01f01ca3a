"""
香港交易所直接PDF爬虫
基于发现的URL模式直接构造PDF链接
"""

import os
import requests
from datetime import datetime, timedelta
import calendar
from tqdm import tqdm
import json


class HKEXDirectScraper:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
        }
        self.session.headers.update(self.headers)

        # 配置
        self.base_url = "https://www1.hkexnews.hk"
        self.download_dir = "downloads"
        self.en_dir = os.path.join(self.download_dir, "english")
        self.tc_dir = os.path.join(self.download_dir, "traditional_chinese")
        self.timeout = 30

        # 创建下载目录
        os.makedirs(self.en_dir, exist_ok=True)
        os.makedirs(self.tc_dir, exist_ok=True)

        # 股票代码映射 (显示代码 -> PDF代码)
        self.stock_code_mapping = {
            "01760": "00741",  # INTRON TECH
            "1760": "00741",
            "741": "00741",
            "00741": "00741",
        }

    def get_pdf_code(self, display_code):
        """获取PDF文件中使用的股票代码"""
        return self.stock_code_mapping.get(display_code, display_code)

    def generate_date_range(self, start_date, end_date):
        """生成日期范围"""
        start = datetime.strptime(start_date, "%d/%m/%Y")
        end = datetime.strptime(end_date, "%d/%m/%Y")

        dates = []
        current = start
        while current <= end:
            dates.append(current)
            current += timedelta(days=1)

        return dates

    def construct_pdf_url(self, date, stock_code, language="default"):
        """构造PDF URL"""
        pdf_code = self.get_pdf_code(stock_code)

        year = date.strftime("%Y")
        month_day = date.strftime("%m%d")
        date_str = date.strftime("%Y%m%d")

        # 根据发现的模式构造URL
        if language == "chinese":
            # 中文版 (某些日期有)
            filename = f"{date_str}{pdf_code}_c.pdf"
        elif language == "english":
            # 英文版 (目前没发现，但保留)
            filename = f"{date_str}{pdf_code}_e.pdf"
        else:
            # 默认版本 (无语言后缀，可能是双语)
            filename = f"{date_str}{pdf_code}.pdf"

        # 构造URL
        url = f"{self.base_url}/listedco/listconews/sehk/{year}/{month_day}/{filename}"

        return url

    def check_pdf_exists(self, url):
        """检查PDF是否存在"""
        try:
            response = self.session.head(url, timeout=self.timeout)
            return response.status_code == 200
        except:
            return False

    def download_pdf(self, url, filepath):
        """下载PDF文件"""
        try:
            response = self.session.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()

            total_size = int(response.headers.get("content-length", 0))

            with open(filepath, "wb") as f:
                if total_size > 0:
                    with tqdm(
                        total=total_size,
                        unit="B",
                        unit_scale=True,
                        desc=os.path.basename(filepath),
                    ) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

            print(f"✓ 下载成功: {filepath}")
            return True

        except Exception as e:
            print(f"✗ 下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False

    def scan_and_download_pdfs(
        self,
        stock_code,
        start_date="01/01/2024",
        end_date="31/12/2024",
        download_english=True,
        download_chinese=True,
    ):
        """扫描并下载PDF文件"""
        print(f"开始扫描股票 {stock_code} 的PDF文件...")
        print(f"日期范围: {start_date} 到 {end_date}")
        print(f"PDF代码: {self.get_pdf_code(stock_code)}")

        # 生成日期范围
        dates = self.generate_date_range(start_date, end_date)
        print(f"总共需要检查 {len(dates)} 个日期")

        found_pdfs = []
        downloaded_count = 0

        for date in tqdm(dates, desc="扫描日期"):
            date_str = date.strftime("%Y-%m-%d")

            # 检查英文版
            if download_english:
                en_url = self.construct_pdf_url(date, stock_code, "en")
                if self.check_pdf_exists(en_url):
                    print(f"\n找到英文PDF: {date_str}")

                    # 下载英文版
                    en_filename = f"en_{date.strftime('%Y%m%d')}_{self.get_pdf_code(stock_code)}_e.pdf"
                    en_filepath = os.path.join(self.en_dir, en_filename)

                    if not os.path.exists(en_filepath):
                        if self.download_pdf(en_url, en_filepath):
                            downloaded_count += 1
                    else:
                        print(f"英文版已存在: {en_filename}")

                    found_pdfs.append(
                        {
                            "date": date_str,
                            "language": "english",
                            "url": en_url,
                            "filename": en_filename,
                        }
                    )

            # 检查中文版
            if download_chinese:
                zh_url = self.construct_pdf_url(date, stock_code, "zh")
                if self.check_pdf_exists(zh_url):
                    print(f"\n找到中文PDF: {date_str}")

                    # 下载中文版
                    zh_filename = f"zh_{date.strftime('%Y%m%d')}_{self.get_pdf_code(stock_code)}_c.pdf"
                    zh_filepath = os.path.join(self.tc_dir, zh_filename)

                    if not os.path.exists(zh_filepath):
                        if self.download_pdf(zh_url, zh_filepath):
                            downloaded_count += 1
                    else:
                        print(f"中文版已存在: {zh_filename}")

                    found_pdfs.append(
                        {
                            "date": date_str,
                            "language": "chinese",
                            "url": zh_url,
                            "filename": zh_filename,
                        }
                    )

        print(f"\n扫描完成!")
        print(f"找到PDF文件: {len(found_pdfs)} 个")
        print(f"新下载文件: {downloaded_count} 个")

        # 保存结果
        results = {
            "timestamp": datetime.now().isoformat(),
            "stock_code": stock_code,
            "pdf_code": self.get_pdf_code(stock_code),
            "date_range": f"{start_date} to {end_date}",
            "total_found": len(found_pdfs),
            "newly_downloaded": downloaded_count,
            "pdfs": found_pdfs,
        }

        with open(f"direct_scan_results_{stock_code}.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"详细结果已保存到: direct_scan_results_{stock_code}.json")

        return found_pdfs

    def test_known_dates(self, stock_code):
        """测试已知存在PDF的日期"""
        print(f"测试股票 {stock_code} 的已知日期...")

        # 基于之前发现的存在的PDF
        known_dates = [
            "29/11/2024",  # 我们知道这个日期有PDF
            "06/11/2024",  # 我们知道这个日期有PDF
            "05/12/2024",  # 尝试这个日期
        ]

        for date_str in known_dates:
            date = datetime.strptime(date_str, "%d/%m/%Y")
            print(f"\n测试日期: {date_str}")

            # 测试英文版
            en_url = self.construct_pdf_url(date, stock_code, "en")
            print(f"英文URL: {en_url}")
            if self.check_pdf_exists(en_url):
                print("✓ 英文版存在")
            else:
                print("✗ 英文版不存在")

            # 测试中文版
            zh_url = self.construct_pdf_url(date, stock_code, "zh")
            print(f"中文URL: {zh_url}")
            if self.check_pdf_exists(zh_url):
                print("✓ 中文版存在")
            else:
                print("✗ 中文版不存在")


def main():
    """主函数"""
    print("香港交易所直接PDF爬虫")
    print("基于发现的URL模式直接构造PDF链接")
    print("=" * 50)

    scraper = HKEXDirectScraper()

    # 股票代码
    stock_code = "01760"  # INTRON TECH

    print(f"目标股票: {stock_code}")
    print(f"PDF代码: {scraper.get_pdf_code(stock_code)}")

    # 首先测试已知日期
    print("\n=== 测试已知日期 ===")
    scraper.test_known_dates(stock_code)

    # 询问是否进行完整扫描
    print("\n=== 完整扫描 ===")
    scan = input("是否进行完整日期扫描? (y/n): ").lower().strip()

    if scan == "y":
        # 扫描并下载
        found_pdfs = scraper.scan_and_download_pdfs(
            stock_code=stock_code,
            start_date="01/01/2024",
            end_date="31/12/2024",
            download_english=True,
            download_chinese=True,
        )

        print(f"\n🎉 扫描完成!")
        print(f"英文PDF保存在: {scraper.en_dir}")
        print(f"中文PDF保存在: {scraper.tc_dir}")
        print(f"总共找到: {len(found_pdfs)} 个PDF文件")
    else:
        print("跳过完整扫描")


if __name__ == "__main__":
    main()
